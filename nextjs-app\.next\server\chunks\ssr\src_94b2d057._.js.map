{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coding%20Project/Tadabbur%20AI/tadabbur-ai-v2/nextjs-app/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coding%20Project/Tadabbur%20AI/tadabbur-ai-v2/nextjs-app/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\n\nfunction Skeleton({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"skeleton\"\n      className={cn(\"bg-accent animate-pulse rounded-md\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Skeleton }\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 48, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coding%20Project/Tadabbur%20AI/tadabbur-ai-v2/nextjs-app/src/components/loading/loading-spinner.tsx"], "sourcesContent": ["import { Loader2 } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\nimport { Skeleton } from \"@/components/ui/skeleton\"\n\ninterface LoadingSpinnerProps {\n  size?: \"sm\" | \"md\" | \"lg\"\n  className?: string\n  text?: string\n}\n\nexport function LoadingSpinner({ size = \"md\", className, text }: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: \"h-4 w-4\",\n    md: \"h-6 w-6\",\n    lg: \"h-8 w-8\"\n  }\n\n  return (\n    <div className={cn(\"flex items-center justify-center\", className)}>\n      <div className=\"flex flex-col items-center space-y-2\">\n        <Loader2 className={cn(\"animate-spin text-primary\", sizeClasses[size])} />\n        {text && (\n          <p className=\"text-sm text-muted-foreground\">{text}</p>\n        )}\n      </div>\n    </div>\n  )\n}\n\nexport function PageLoading() {\n  return (\n    <div className=\"min-h-screen flex items-center justify-center\">\n      <LoadingSpinner size=\"lg\" text=\"Loading...\" />\n    </div>\n  )\n}\n\nexport function ComponentLoading({ className }: { className?: string }) {\n  return (\n    <div className={cn(\"flex items-center justify-center p-8\", className)}>\n      <LoadingSpinner text=\"Loading...\" />\n    </div>\n  )\n}\n\nexport function InlineLoading({ text = \"Loading...\" }: { text?: string }) {\n  return (\n    <div className=\"flex items-center space-x-2\">\n      <Loader2 className=\"h-4 w-4 animate-spin\" />\n      <span className=\"text-sm text-muted-foreground\">{text}</span>\n    </div>\n  )\n}\n\nexport function CardSkeleton() {\n  return (\n    <div className=\"p-6 border border-border rounded-lg bg-card\">\n      <div className=\"flex items-center space-x-3 mb-4\">\n        <Skeleton className=\"h-9 w-9 rounded-lg\" />\n        <Skeleton className=\"h-4 w-32\" />\n      </div>\n      <Skeleton className=\"h-4 w-full mb-2\" />\n      <Skeleton className=\"h-4 w-3/4\" />\n    </div>\n  )\n}\n\nexport function ListSkeleton({ count = 3 }: { count?: number }) {\n  return (\n    <div className=\"space-y-4\">\n      {Array.from({ length: count }).map((_, i) => (\n        <div key={i} className=\"flex items-center space-x-4\">\n          <Skeleton className=\"h-12 w-12 rounded-full\" />\n          <div className=\"space-y-2 flex-1\">\n            <Skeleton className=\"h-4 w-full\" />\n            <Skeleton className=\"h-4 w-2/3\" />\n          </div>\n        </div>\n      ))}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;AACA;;;;;AAQO,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,SAAS,EAAE,IAAI,EAAuB;IAClF,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;kBACrD,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,iNAAA,CAAA,UAAO;oBAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B,WAAW,CAAC,KAAK;;;;;;gBACpE,sBACC,8OAAC;oBAAE,WAAU;8BAAiC;;;;;;;;;;;;;;;;;AAKxD;AAEO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAe,MAAK;YAAK,MAAK;;;;;;;;;;;AAGrC;AAEO,SAAS,iBAAiB,EAAE,SAAS,EAA0B;IACpE,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;kBACzD,cAAA,8OAAC;YAAe,MAAK;;;;;;;;;;;AAG3B;AAEO,SAAS,cAAc,EAAE,OAAO,YAAY,EAAqB;IACtE,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,iNAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;0BACnB,8OAAC;gBAAK,WAAU;0BAAiC;;;;;;;;;;;;AAGvD;AAEO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,oIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,8OAAC,oIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;;;;;;;0BAEtB,8OAAC,oIAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;0BACpB,8OAAC,oIAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;;;;;;;AAG1B;AAEO,SAAS,aAAa,EAAE,QAAQ,CAAC,EAAsB;IAC5D,qBACE,8OAAC;QAAI,WAAU;kBACZ,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,GAAG,CAAC,CAAC,GAAG,kBACrC,8OAAC;gBAAY,WAAU;;kCACrB,8OAAC,oIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC,oIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;;;eAJd;;;;;;;;;;AAUlB", "debugId": null}}, {"offset": {"line": 265, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coding%20Project/Tadabbur%20AI/tadabbur-ai-v2/nextjs-app/src/app/loading.tsx"], "sourcesContent": ["import { PageLoading } from \"@/components/loading/loading-spinner\"\n\nexport default function Loading() {\n  return <PageLoading />\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBAAO,8OAAC,mJAAA,CAAA,cAAW;;;;;AACrB", "debugId": null}}]}