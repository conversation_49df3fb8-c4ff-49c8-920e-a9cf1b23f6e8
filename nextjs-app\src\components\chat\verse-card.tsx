import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardFooter } from "@/components/ui/card"
import { <PERSON><PERSON><PERSON>, Heart, Share, Copy } from "lucide-react"

interface Verse {
  id: string
  arabic: string
  translation: string
  reference: string
}

interface VerseCardProps {
  verse: Verse
  onSelect: () => void
}

export function VerseCard({ verse, onSelect }: VerseCardProps) {
  const handleCopy = () => {
    navigator.clipboard.writeText(`${verse.arabic}\n\n${verse.translation}\n\n- ${verse.reference}`)
  }

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: verse.reference,
        text: `${verse.arabic}\n\n${verse.translation}`,
      })
    }
  }

  return (
    <Card className="hover:shadow-sm transition-shadow">
      <CardContent className="p-4 space-y-4">
        {/* Arabic Text */}
        <div className="text-right">
          <p className="arabic-text text-lg leading-relaxed">
            {verse.arabic}
          </p>
        </div>

        {/* Translation */}
        <div>
          <p className="text-sm text-muted-foreground leading-relaxed">
            {verse.translation}
          </p>
        </div>
      </CardContent>

      <CardFooter className="flex items-center justify-between pt-2 border-t border-border">
        <span className="text-xs font-medium text-primary">
          {verse.reference}
        </span>

        <div className="flex items-center space-x-1">
          <Button
            variant="ghost"
            size="icon"
            onClick={onSelect}
            className="h-8 w-8"
            title="Study this verse"
          >
            <BookOpen className="h-3 w-3" />
          </Button>
          
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8"
            title="Add to favorites"
          >
            <Heart className="h-3 w-3" />
          </Button>
          
          <Button
            variant="ghost"
            size="icon"
            onClick={handleCopy}
            className="h-8 w-8"
            title="Copy verse"
          >
            <Copy className="h-3 w-3" />
          </Button>
          
          <Button
            variant="ghost"
            size="icon"
            onClick={handleShare}
            className="h-8 w-8"
            title="Share verse"
          >
            <Share className="h-3 w-3" />
          </Button>
        </div>
      </CardFooter>
    </Card>
  )
}
