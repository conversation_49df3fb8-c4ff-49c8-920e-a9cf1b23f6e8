{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coding%20Project/Tadabbur%20AI/tadabbur-ai-v2/nextjs-app/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coding%20Project/Tadabbur%20AI/tadabbur-ai-v2/nextjs-app/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: \"default\" | \"destructive\" | \"outline\" | \"secondary\" | \"ghost\" | \"link\"\n  size?: \"default\" | \"sm\" | \"lg\" | \"icon\"\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = \"default\", size = \"default\", ...props }, ref) => {\n    return (\n      <button\n        className={cn(\n          \"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none\",\n          {\n            \"bg-primary text-primary-foreground hover:bg-primary/90\": variant === \"default\",\n            \"bg-destructive text-destructive-foreground hover:bg-destructive/90\": variant === \"destructive\",\n            \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\": variant === \"outline\",\n            \"bg-secondary text-secondary-foreground hover:bg-secondary/80\": variant === \"secondary\",\n            \"hover:bg-accent hover:text-accent-foreground\": variant === \"ghost\",\n            \"text-primary underline-offset-4 hover:underline\": variant === \"link\",\n          },\n          {\n            \"h-10 px-4 py-2\": size === \"default\",\n            \"h-9 rounded-md px-3\": size === \"sm\",\n            \"h-11 rounded-md px-8\": size === \"lg\",\n            \"h-10 w-10\": size === \"icon\",\n          },\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,GAAG,OAAO,EAAE;IAC/D,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iPACA;YACE,0DAA0D,YAAY;YACtE,sEAAsE,YAAY;YAClF,kFAAkF,YAAY;YAC9F,gEAAgE,YAAY;YAC5E,gDAAgD,YAAY;YAC5D,mDAAmD,YAAY;QACjE,GACA;YACE,kBAAkB,SAAS;YAC3B,uBAAuB,SAAS;YAChC,wBAAwB,SAAS;YACjC,aAAa,SAAS;QACxB,GACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coding%20Project/Tadabbur%20AI/tadabbur-ai-v2/nextjs-app/src/components/sidebar.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport Image from \"next/image\"\nimport Link from \"next/link\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { Folder, Plus, HelpCircle, Crown, Settings, X, Menu } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface SidebarProps {\n  className?: string\n  folders?: Array<{\n    id: number\n    name: string\n    count: number\n    icon?: string\n  }>\n  currentUser?: {\n    name: string\n    email: string\n    avatar?: string\n  }\n}\n\nexport function Sidebar({ className, folders = [], currentUser }: SidebarProps) {\n  const [isOpen, setIsOpen] = useState(false)\n\n  const defaultFolders = [\n    { id: 1, name: \"All notes\", count: 4, icon: \"📝\" },\n    { id: 2, name: \"bio\", count: 1, icon: \"👤\" },\n  ]\n\n  const displayFolders = folders.length > 0 ? folders : defaultFolders\n\n  return (\n    <>\n      {/* Mobile menu button */}\n      <Button\n        variant=\"ghost\"\n        size=\"icon\"\n        className=\"fixed top-4 left-4 z-50 md:hidden\"\n        onClick={() => setIsOpen(true)}\n      >\n        <Menu className=\"h-6 w-6\" />\n      </Button>\n\n      {/* Mobile overlay */}\n      {isOpen && (\n        <div\n          className=\"fixed inset-0 bg-black/50 z-40 md:hidden\"\n          onClick={() => setIsOpen(false)}\n        />\n      )}\n\n      {/* Sidebar */}\n      <aside\n        className={cn(\n          \"fixed left-0 top-0 z-50 h-full w-80 bg-background border-r border-border transform transition-transform duration-300 ease-in-out md:relative md:translate-x-0\",\n          isOpen ? \"translate-x-0\" : \"-translate-x-full\",\n          className\n        )}\n      >\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b border-border\">\n          <Link href=\"/\" className=\"flex items-center space-x-3\">\n            <Image\n              src=\"/logo.png\"\n              alt=\"Tadabbur AI\"\n              width={32}\n              height={32}\n              className=\"rounded\"\n            />\n            <span className=\"text-xl font-semibold\">Tadabbur AI</span>\n          </Link>\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            className=\"md:hidden\"\n            onClick={() => setIsOpen(false)}\n          >\n            <X className=\"h-5 w-5\" />\n          </Button>\n        </div>\n\n        {/* Folders Section */}\n        <div className=\"p-6\">\n          <div className=\"flex items-center space-x-2 mb-4\">\n            <Folder className=\"h-5 w-5 text-muted-foreground\" />\n            <span className=\"font-medium\">Folders</span>\n          </div>\n\n          <div className=\"space-y-2\">\n            {displayFolders.map((folder) => (\n              <Link\n                key={folder.id}\n                href={`/notes?folder=${folder.id}`}\n                className=\"flex items-center justify-between p-3 rounded-lg hover:bg-accent transition-colors\"\n              >\n                <div className=\"flex items-center space-x-3\">\n                  <span className=\"text-lg\">{folder.icon || \"📁\"}</span>\n                  <span className=\"text-sm\">{folder.name}</span>\n                </div>\n                <span className=\"text-xs text-muted-foreground\">({folder.count})</span>\n              </Link>\n            ))}\n          </div>\n\n          <Button variant=\"ghost\" className=\"w-full justify-start mt-4\">\n            <Plus className=\"h-4 w-4 mr-2\" />\n            Create new folder\n          </Button>\n        </div>\n\n        {/* Bottom Section */}\n        <div className=\"absolute bottom-0 left-0 right-0 p-6 space-y-4\">\n          {/* Support */}\n          <Button variant=\"ghost\" className=\"w-full justify-start\">\n            <HelpCircle className=\"h-4 w-4 mr-2\" />\n            Support\n          </Button>\n\n          {/* Upgrade */}\n          <div className=\"space-y-2\">\n            <Button variant=\"ghost\" className=\"w-full justify-start\">\n              <Crown className=\"h-4 w-4 mr-2\" />\n              Upgrade plan\n            </Button>\n            <p className=\"text-xs text-muted-foreground\">\n              Get more features and unlimited access\n            </p>\n            <div className=\"space-y-1\">\n              <div className=\"flex justify-between text-xs\">\n                <span>3 / 3 Notes free</span>\n              </div>\n              <div className=\"w-full bg-secondary rounded-full h-2\">\n                <div className=\"bg-primary h-2 rounded-full w-full\"></div>\n              </div>\n            </div>\n          </div>\n\n          {/* Profile */}\n          {currentUser && (\n            <div className=\"flex items-center space-x-3 p-3 rounded-lg bg-accent\">\n              <div className=\"w-8 h-8 bg-primary rounded-full flex items-center justify-center text-primary-foreground text-sm font-medium\">\n                {currentUser.name.split(' ').map(n => n[0]).join('').toUpperCase()}\n              </div>\n              <div className=\"flex-1 min-w-0\">\n                <p className=\"text-sm font-medium truncate\">{currentUser.name}</p>\n                <p className=\"text-xs text-muted-foreground truncate\">{currentUser.email}</p>\n              </div>\n              <Button variant=\"ghost\" size=\"icon\">\n                <Settings className=\"h-4 w-4\" />\n              </Button>\n            </div>\n          )}\n        </div>\n      </aside>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAPA;;;;;;;AAwBO,SAAS,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,EAAE,WAAW,EAAgB;;IAC5E,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,iBAAiB;QACrB;YAAE,IAAI;YAAG,MAAM;YAAa,OAAO;YAAG,MAAM;QAAK;QACjD;YAAE,IAAI;YAAG,MAAM;YAAO,OAAO;YAAG,MAAM;QAAK;KAC5C;IAED,MAAM,iBAAiB,QAAQ,MAAM,GAAG,IAAI,UAAU;IAEtD,qBACE;;0BAEE,6LAAC,qIAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,MAAK;gBACL,WAAU;gBACV,SAAS,IAAM,UAAU;0BAEzB,cAAA,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;;;;;;YAIjB,wBACC,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,UAAU;;;;;;0BAK7B,6LAAC;gBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iKACA,SAAS,kBAAkB,qBAC3B;;kCAIF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,6LAAC,gIAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;kDAEZ,6LAAC;wCAAK,WAAU;kDAAwB;;;;;;;;;;;;0CAE1C,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,UAAU;0CAEzB,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAKjB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCAAK,WAAU;kDAAc;;;;;;;;;;;;0CAGhC,6LAAC;gCAAI,WAAU;0CACZ,eAAe,GAAG,CAAC,CAAC,uBACnB,6LAAC,+JAAA,CAAA,UAAI;wCAEH,MAAM,CAAC,cAAc,EAAE,OAAO,EAAE,EAAE;wCAClC,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAW,OAAO,IAAI,IAAI;;;;;;kEAC1C,6LAAC;wDAAK,WAAU;kEAAW,OAAO,IAAI;;;;;;;;;;;;0DAExC,6LAAC;gDAAK,WAAU;;oDAAgC;oDAAE,OAAO,KAAK;oDAAC;;;;;;;;uCAR1D,OAAO,EAAE;;;;;;;;;;0CAapB,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,WAAU;;kDAChC,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;kCAMrC,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,WAAU;;kDAChC,6LAAC,iOAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAKzC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,WAAU;;0DAChC,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGpC,6LAAC;wCAAE,WAAU;kDAAgC;;;;;;kDAG7C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;8DAAK;;;;;;;;;;;0DAER,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;4BAMpB,6BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACZ,YAAY,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,WAAW;;;;;;kDAElE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAgC,YAAY,IAAI;;;;;;0DAC7D,6LAAC;gDAAE,WAAU;0DAA0C,YAAY,KAAK;;;;;;;;;;;;kDAE1E,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;kDAC3B,cAAA,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpC;GAvIgB;KAAA", "debugId": null}}, {"offset": {"line": 484, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coding%20Project/Tadabbur%20AI/tadabbur-ai-v2/nextjs-app/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 519, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coding%20Project/Tadabbur%20AI/tadabbur-ai-v2/nextjs-app/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,6LAAC,qKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 581, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coding%20Project/Tadabbur%20AI/tadabbur-ai-v2/nextjs-app/src/components/chat/chat-message.tsx"], "sourcesContent": ["import { ChatMessage as ChatMessageType } from \"@/types\"\nimport { <PERSON><PERSON>, User } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\nimport { Avatar, AvatarFallback } from \"@/components/ui/avatar\"\n\ninterface ChatMessageProps {\n  message: ChatMessageType\n}\n\nexport function ChatMessage({ message }: ChatMessageProps) {\n  const isUser = message.role === \"user\"\n\n  return (\n    <div className={cn(\n      \"flex gap-3 max-w-4xl\",\n      isUser ? \"ml-auto flex-row-reverse\" : \"mr-auto\"\n    )}>\n      {/* Avatar */}\n      <Avatar className=\"w-8 h-8\">\n        <AvatarFallback className={cn(\n          isUser\n            ? \"bg-primary text-primary-foreground\"\n            : \"bg-muted text-muted-foreground\"\n        )}>\n          {isUser ? (\n            <User className=\"h-4 w-4\" />\n          ) : (\n            <Bot className=\"h-4 w-4\" />\n          )}\n        </AvatarFallback>\n      </Avatar>\n\n      {/* Message Content */}\n      <div className={cn(\n        \"flex-1 space-y-2\",\n        isUser ? \"text-right\" : \"text-left\"\n      )}>\n        <div className={cn(\n          \"inline-block p-3 rounded-lg max-w-[80%]\",\n          isUser\n            ? \"bg-primary text-primary-foreground ml-auto\"\n            : \"bg-muted text-foreground mr-auto\"\n        )}>\n          <p className=\"text-sm whitespace-pre-wrap\">{message.content}</p>\n        </div>\n        \n        <p className=\"text-xs text-muted-foreground\">\n          {message.timestamp.toLocaleTimeString([], { \n            hour: '2-digit', \n            minute: '2-digit' \n          })}\n        </p>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;AAAA;AACA;AACA;;;;;AAMO,SAAS,YAAY,EAAE,OAAO,EAAoB;IACvD,MAAM,SAAS,QAAQ,IAAI,KAAK;IAEhC,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,wBACA,SAAS,6BAA6B;;0BAGtC,6LAAC,qIAAA,CAAA,SAAM;gBAAC,WAAU;0BAChB,cAAA,6LAAC,qIAAA,CAAA,iBAAc;oBAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAC1B,SACI,uCACA;8BAEH,uBACC,6LAAC,qMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;6CAEhB,6LAAC,mMAAA,CAAA,MAAG;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAMrB,6LAAC;gBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,oBACA,SAAS,eAAe;;kCAExB,6LAAC;wBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,2CACA,SACI,+CACA;kCAEJ,cAAA,6LAAC;4BAAE,WAAU;sCAA+B,QAAQ,OAAO;;;;;;;;;;;kCAG7D,6LAAC;wBAAE,WAAU;kCACV,QAAQ,SAAS,CAAC,kBAAkB,CAAC,EAAE,EAAE;4BACxC,MAAM;4BACN,QAAQ;wBACV;;;;;;;;;;;;;;;;;;AAKV;KA9CgB", "debugId": null}}, {"offset": {"line": 679, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coding%20Project/Tadabbur%20AI/tadabbur-ai-v2/nextjs-app/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border border-border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\"text-2xl font-semibold leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p ref={ref} className={cn(\"text-sm text-muted-foreground\", className)} {...props} />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0EACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sDAAsD;QACnE,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAE,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;;AAEnF,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 782, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coding%20Project/Tadabbur%20AI/tadabbur-ai-v2/nextjs-app/src/components/chat/verse-card.tsx"], "sourcesContent": ["import { But<PERSON> } from \"@/components/ui/button\"\nimport { <PERSON>, Card<PERSON>ontent, CardFooter } from \"@/components/ui/card\"\nimport { <PERSON><PERSON><PERSON>, Heart, Share, Copy } from \"lucide-react\"\n\ninterface Verse {\n  id: string\n  arabic: string\n  translation: string\n  reference: string\n}\n\ninterface VerseCardProps {\n  verse: Verse\n  onSelect: () => void\n}\n\nexport function VerseCard({ verse, onSelect }: VerseCardProps) {\n  const handleCopy = () => {\n    navigator.clipboard.writeText(`${verse.arabic}\\n\\n${verse.translation}\\n\\n- ${verse.reference}`)\n  }\n\n  const handleShare = () => {\n    if (navigator.share) {\n      navigator.share({\n        title: verse.reference,\n        text: `${verse.arabic}\\n\\n${verse.translation}`,\n      })\n    }\n  }\n\n  return (\n    <Card className=\"hover:shadow-sm transition-shadow\">\n      <CardContent className=\"p-4 space-y-4\">\n        {/* Arabic Text */}\n        <div className=\"text-right\">\n          <p className=\"arabic-text text-lg leading-relaxed\">\n            {verse.arabic}\n          </p>\n        </div>\n\n        {/* Translation */}\n        <div>\n          <p className=\"text-sm text-muted-foreground leading-relaxed\">\n            {verse.translation}\n          </p>\n        </div>\n      </CardContent>\n\n      <CardFooter className=\"flex items-center justify-between pt-2 border-t border-border\">\n        <span className=\"text-xs font-medium text-primary\">\n          {verse.reference}\n        </span>\n\n        <div className=\"flex items-center space-x-1\">\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            onClick={onSelect}\n            className=\"h-8 w-8\"\n            title=\"Study this verse\"\n          >\n            <BookOpen className=\"h-3 w-3\" />\n          </Button>\n          \n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            className=\"h-8 w-8\"\n            title=\"Add to favorites\"\n          >\n            <Heart className=\"h-3 w-3\" />\n          </Button>\n          \n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            onClick={handleCopy}\n            className=\"h-8 w-8\"\n            title=\"Copy verse\"\n          >\n            <Copy className=\"h-3 w-3\" />\n          </Button>\n          \n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            onClick={handleShare}\n            className=\"h-8 w-8\"\n            title=\"Share verse\"\n          >\n            <Share className=\"h-3 w-3\" />\n          </Button>\n        </div>\n      </CardFooter>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;;;;;AAcO,SAAS,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAkB;IAC3D,MAAM,aAAa;QACjB,UAAU,SAAS,CAAC,SAAS,CAAC,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,MAAM,WAAW,CAAC,MAAM,EAAE,MAAM,SAAS,EAAE;IACjG;IAEA,MAAM,cAAc;QAClB,IAAI,UAAU,KAAK,EAAE;YACnB,UAAU,KAAK,CAAC;gBACd,OAAO,MAAM,SAAS;gBACtB,MAAM,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,MAAM,WAAW,EAAE;YACjD;QACF;IACF;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCACV,MAAM,MAAM;;;;;;;;;;;kCAKjB,6LAAC;kCACC,cAAA,6LAAC;4BAAE,WAAU;sCACV,MAAM,WAAW;;;;;;;;;;;;;;;;;0BAKxB,6LAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,6LAAC;wBAAK,WAAU;kCACb,MAAM,SAAS;;;;;;kCAGlB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;gCACV,OAAM;0CAEN,cAAA,6LAAC,iNAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;0CAGtB,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,OAAM;0CAEN,cAAA,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;0CAGnB,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;gCACV,OAAM;0CAEN,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAGlB,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;gCACV,OAAM;0CAEN,cAAA,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM7B;KAhFgB", "debugId": null}}, {"offset": {"line": 965, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coding%20Project/Tadabbur%20AI/tadabbur-ai-v2/nextjs-app/src/components/chat/chat-interface.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useRef, useEffect } from \"react\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Textarea } from \"@/components/ui/textarea\"\nimport { ChatMessage } from \"@/components/chat/chat-message\"\nimport { VerseCard } from \"@/components/chat/verse-card\"\nimport { Send, Mic, Upload, Loader2 } from \"lucide-react\"\nimport { ChatMessage as ChatMessageType } from \"@/types\"\n\ninterface ChatInterfaceProps {\n  onQueryChange: (query: string) => void\n  onVerseSelect: (verseKey: string) => void\n  currentQuery: string\n}\n\nexport function ChatInterface({ onQueryChange, onVerseSelect, currentQuery }: ChatInterfaceProps) {\n  const [messages, setMessages] = useState<ChatMessageType[]>([\n    {\n      id: \"1\",\n      role: \"assistant\",\n      content: \"Welcome to Tadabbur AI! I'm here to help you explore and understand the Quran. You can ask me about verses, concepts, or upload images for analysis. How can I assist you today?\",\n      timestamp: new Date()\n    }\n  ])\n  const [inputValue, setInputValue] = useState(\"\")\n  const [isLoading, setIsLoading] = useState(false)\n  const [isListening, setIsListening] = useState(false)\n  const messagesEndRef = useRef<HTMLDivElement>(null)\n  const textareaRef = useRef<HTMLTextAreaElement>(null)\n\n  // Mock verses data - in real app this would come from API\n  const mockVerses = [\n    {\n      id: \"2:255\",\n      arabic: \"اللَّهُ لَا إِلَٰهَ إِلَّا هُوَ الْحَيُّ الْقَيُّومُ ۚ لَا تَأْخُذُهُ سِنَةٌ وَلَا نَوْمٌ\",\n      translation: \"Allah - there is no deity except Him, the Ever-Living, the Sustainer of existence. Neither drowsiness overtakes Him nor sleep.\",\n      reference: \"Al-Baqarah 2:255\"\n    }\n  ]\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: \"smooth\" })\n  }\n\n  useEffect(() => {\n    scrollToBottom()\n  }, [messages])\n\n  useEffect(() => {\n    onQueryChange(inputValue)\n  }, [inputValue, onQueryChange])\n\n  const handleSendMessage = async () => {\n    if (!inputValue.trim() || isLoading) return\n\n    const userMessage: ChatMessageType = {\n      id: Date.now().toString(),\n      role: \"user\",\n      content: inputValue,\n      timestamp: new Date()\n    }\n\n    setMessages(prev => [...prev, userMessage])\n    setInputValue(\"\")\n    setIsLoading(true)\n\n    // Simulate AI response\n    setTimeout(() => {\n      const aiResponse: ChatMessageType = {\n        id: (Date.now() + 1).toString(),\n        role: \"assistant\",\n        content: `I understand you're asking about \"${userMessage.content}\". Let me help you explore this topic with relevant verses and insights.`,\n        timestamp: new Date()\n      }\n      setMessages(prev => [...prev, aiResponse])\n      setIsLoading(false)\n    }, 1500)\n  }\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === \"Enter\" && !e.shiftKey) {\n      e.preventDefault()\n      handleSendMessage()\n    }\n  }\n\n  const handleVoiceInput = () => {\n    setIsListening(!isListening)\n    // Voice input functionality would be implemented here\n  }\n\n  const handleFileUpload = () => {\n    // File upload functionality would be implemented here\n    console.log(\"File upload clicked\")\n  }\n\n  return (\n    <div className=\"flex flex-col h-full\">\n      {/* Chat Header */}\n      <div className=\"p-4 border-b border-border\">\n        <h1 className=\"text-xl font-semibold\">Quranic Study Assistant</h1>\n        <p className=\"text-sm text-muted-foreground\">\n          Ask questions, search verses, or upload images for analysis\n        </p>\n      </div>\n\n      {/* Messages Area */}\n      <div className=\"flex-1 overflow-y-auto p-4 space-y-4\">\n        {messages.map((message) => (\n          <ChatMessage key={message.id} message={message} />\n        ))}\n\n        {/* Sample Verses */}\n        {messages.length <= 2 && (\n          <div className=\"space-y-4\">\n            <p className=\"text-sm text-muted-foreground text-center\">\n              Here are some verses to get you started:\n            </p>\n            {mockVerses.map((verse) => (\n              <VerseCard\n                key={verse.id}\n                verse={verse}\n                onSelect={() => onVerseSelect(verse.id)}\n              />\n            ))}\n          </div>\n        )}\n\n        {isLoading && (\n          <div className=\"flex items-center space-x-2 text-muted-foreground\">\n            <Loader2 className=\"h-4 w-4 animate-spin\" />\n            <span className=\"text-sm\">AI is thinking...</span>\n          </div>\n        )}\n\n        <div ref={messagesEndRef} />\n      </div>\n\n      {/* Input Area */}\n      <div className=\"p-4 border-t border-border\">\n        <div className=\"flex items-end space-x-2\">\n          <div className=\"flex-1\">\n            <Textarea\n              ref={textareaRef}\n              value={inputValue}\n              onChange={(e) => setInputValue(e.target.value)}\n              onKeyPress={handleKeyPress}\n              placeholder=\"Ask about verses, concepts, or upload an image...\"\n              className=\"min-h-[60px] max-h-32 resize-none\"\n              rows={2}\n            />\n          </div>\n          \n          <div className=\"flex flex-col space-y-2\">\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={handleVoiceInput}\n              className={isListening ? \"text-primary\" : \"text-muted-foreground\"}\n              title=\"Voice input\"\n            >\n              <Mic className=\"h-4 w-4\" />\n            </Button>\n            \n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={handleFileUpload}\n              className=\"text-muted-foreground\"\n              title=\"Upload image\"\n            >\n              <Upload className=\"h-4 w-4\" />\n            </Button>\n            \n            <Button\n              onClick={handleSendMessage}\n              disabled={!inputValue.trim() || isLoading}\n              size=\"icon\"\n            >\n              <Send className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        </div>\n        \n        <p className=\"text-xs text-muted-foreground mt-2\">\n          Press Enter to send, Shift+Enter for new line\n        </p>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AAPA;;;;;;;AAgBO,SAAS,cAAc,EAAE,aAAa,EAAE,aAAa,EAAE,YAAY,EAAsB;;IAC9F,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;QAC1D;YACE,IAAI;YACJ,MAAM;YACN,SAAS;YACT,WAAW,IAAI;QACjB;KACD;IACD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAuB;IAEhD,0DAA0D;IAC1D,MAAM,aAAa;QACjB;YACE,IAAI;YACJ,QAAQ;YACR,aAAa;YACb,WAAW;QACb;KACD;IAED,MAAM,iBAAiB;QACrB,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG;QAAC;KAAS;IAEb,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,cAAc;QAChB;kCAAG;QAAC;QAAY;KAAc;IAE9B,MAAM,oBAAoB;QACxB,IAAI,CAAC,WAAW,IAAI,MAAM,WAAW;QAErC,MAAM,cAA+B;YACnC,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,MAAM;YACN,SAAS;YACT,WAAW,IAAI;QACjB;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAC1C,cAAc;QACd,aAAa;QAEb,uBAAuB;QACvB,WAAW;YACT,MAAM,aAA8B;gBAClC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,MAAM;gBACN,SAAS,CAAC,kCAAkC,EAAE,YAAY,OAAO,CAAC,wEAAwE,CAAC;gBAC3I,WAAW,IAAI;YACjB;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAW;YACzC,aAAa;QACf,GAAG;IACL;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,MAAM,mBAAmB;QACvB,eAAe,CAAC;IAChB,sDAAsD;IACxD;IAEA,MAAM,mBAAmB;QACvB,sDAAsD;QACtD,QAAQ,GAAG,CAAC;IACd;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwB;;;;;;kCACtC,6LAAC;wBAAE,WAAU;kCAAgC;;;;;;;;;;;;0BAM/C,6LAAC;gBAAI,WAAU;;oBACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC,gJAAA,CAAA,cAAW;4BAAkB,SAAS;2BAArB,QAAQ,EAAE;;;;;oBAI7B,SAAS,MAAM,IAAI,mBAClB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAA4C;;;;;;4BAGxD,WAAW,GAAG,CAAC,CAAC,sBACf,6LAAC,8IAAA,CAAA,YAAS;oCAER,OAAO;oCACP,UAAU,IAAM,cAAc,MAAM,EAAE;mCAFjC,MAAM,EAAE;;;;;;;;;;;oBAQpB,2BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,oNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;kCAI9B,6LAAC;wBAAI,KAAK;;;;;;;;;;;;0BAIZ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,uIAAA,CAAA,WAAQ;oCACP,KAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,YAAY;oCACZ,aAAY;oCACZ,WAAU;oCACV,MAAM;;;;;;;;;;;0CAIV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAW,cAAc,iBAAiB;wCAC1C,OAAM;kDAEN,cAAA,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;;;;;;kDAGjB,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;wCACV,OAAM;kDAEN,cAAA,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAGpB,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,UAAU,CAAC,WAAW,IAAI,MAAM;wCAChC,MAAK;kDAEL,cAAA,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAKtB,6LAAC;wBAAE,WAAU;kCAAqC;;;;;;;;;;;;;;;;;;AAM1D;GA/KgB;KAAA", "debugId": null}}, {"offset": {"line": 1301, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coding%20Project/Tadabbur%20AI/tadabbur-ai-v2/nextjs-app/src/components/ui/tabs.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\nimport { cn } from \"@/lib/utils\"\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,OAAO,mKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;;AAGb,SAAS,WAAW,GAAG,mKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1366, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coding%20Project/Tadabbur%20AI/tadabbur-ai-v2/nextjs-app/src/components/notes/summary-tab.tsx"], "sourcesContent": ["\"use client\"\n\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Textarea } from \"@/components/ui/textarea\"\nimport { Loader2, <PERSON>rk<PERSON>, RefreshCw } from \"lucide-react\"\n\ninterface SummaryTabProps {\n  content: string\n  onContentChange: (content: string) => void\n  isGenerating: boolean\n  onGenerate: () => void\n  selectedVerse?: string | null\n  currentQuery?: string\n}\n\nexport function SummaryTab({\n  content,\n  onContentChange,\n  isGenerating,\n  onGenerate,\n  selectedVerse,\n  currentQuery\n}: SummaryTabProps) {\n  const hasContent = content.trim().length > 0\n  const hasQuery = (currentQuery && currentQuery.trim().length > 0) || selectedVerse\n\n  return (\n    <div className=\"h-full flex flex-col space-y-4\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <h3 className=\"font-medium\">Study Summary</h3>\n        <div className=\"flex items-center space-x-2\">\n          {hasContent && (\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={onGenerate}\n              disabled={isGenerating}\n            >\n              <RefreshCw className=\"h-3 w-3 mr-1\" />\n              Regenerate\n            </Button>\n          )}\n          {hasQuery && !hasContent && (\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={onGenerate}\n              disabled={isGenerating}\n            >\n              {isGenerating ? (\n                <Loader2 className=\"h-3 w-3 mr-1 animate-spin\" />\n              ) : (\n                <Sparkles className=\"h-3 w-3 mr-1\" />\n              )}\n              Generate Notes\n            </Button>\n          )}\n        </div>\n      </div>\n\n      {/* Content Area */}\n      <div className=\"flex-1 flex flex-col\">\n        {isGenerating ? (\n          <div className=\"flex-1 flex items-center justify-center\">\n            <div className=\"text-center space-y-3\">\n              <Loader2 className=\"h-8 w-8 animate-spin mx-auto text-primary\" />\n              <p className=\"text-sm text-muted-foreground\">\n                Generating AI-powered study notes...\n              </p>\n            </div>\n          </div>\n        ) : hasContent ? (\n          <Textarea\n            value={content}\n            onChange={(e) => onContentChange(e.target.value)}\n            placeholder=\"Your study notes will appear here...\"\n            className=\"flex-1 resize-none font-mono text-sm\"\n          />\n        ) : (\n          <div className=\"flex-1 flex items-center justify-center\">\n            <div className=\"text-center space-y-4 max-w-sm\">\n              <div className=\"w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto\">\n                <Sparkles className=\"h-8 w-8 text-muted-foreground\" />\n              </div>\n              <div className=\"space-y-2\">\n                <h4 className=\"font-medium\">No notes yet</h4>\n                <p className=\"text-sm text-muted-foreground\">\n                  Start typing in the chat or select a verse to automatically generate study notes\n                </p>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Tips */}\n      {!hasContent && !isGenerating && (\n        <div className=\"p-3 bg-muted rounded-lg\">\n          <p className=\"text-xs text-muted-foreground\">\n            💡 <strong>Tip:</strong> Notes are automatically generated when you ask questions or select verses. \n            You can edit and customize them as needed.\n          </p>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAJA;;;;;AAeO,SAAS,WAAW,EACzB,OAAO,EACP,eAAe,EACf,YAAY,EACZ,UAAU,EACV,aAAa,EACb,YAAY,EACI;IAChB,MAAM,aAAa,QAAQ,IAAI,GAAG,MAAM,GAAG;IAC3C,MAAM,WAAW,AAAC,gBAAgB,aAAa,IAAI,GAAG,MAAM,GAAG,KAAM;IAErE,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAc;;;;;;kCAC5B,6LAAC;wBAAI,WAAU;;4BACZ,4BACC,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,UAAU;;kDAEV,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;4BAIzC,YAAY,CAAC,4BACZ,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,UAAU;;oCAET,6BACC,6LAAC,oNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;6DAEnB,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCACpB;;;;;;;;;;;;;;;;;;;0BAQV,6LAAC;gBAAI,WAAU;0BACZ,6BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,oNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,6LAAC;gCAAE,WAAU;0CAAgC;;;;;;;;;;;;;;;;2BAK/C,2BACF,6LAAC,uIAAA,CAAA,WAAQ;oBACP,OAAO;oBACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;oBAC/C,aAAY;oBACZ,WAAU;;;;;yCAGZ,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;0CAEtB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAc;;;;;;kDAC5B,6LAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUtD,CAAC,cAAc,CAAC,8BACf,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;;wBAAgC;sCACxC,6LAAC;sCAAO;;;;;;wBAAa;;;;;;;;;;;;;;;;;;AAOpC;KA5FgB", "debugId": null}}, {"offset": {"line": 1603, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coding%20Project/Tadabbur%20AI/tadabbur-ai-v2/nextjs-app/src/components/notes/flashcards-tab.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { Card } from \"@/components/ui/card\"\nimport { RotateCcw, Plus, Shuffle, Eye, EyeOff } from \"lucide-react\"\n\ninterface FlashcardsTabProps {\n  selectedVerse?: string | null\n  currentQuery?: string\n}\n\ninterface Flashcard {\n  id: string\n  question: string\n  answer: string\n}\n\nexport function FlashcardsTab({ selectedVerse, currentQuery }: FlashcardsTabProps) {\n  const [flashcards, setFlashcards] = useState<Flashcard[]>([\n    {\n      id: \"1\",\n      question: \"What is the meaning of 'Tadabbur' in Arabic?\",\n      answer: \"Deep contemplation and reflection, especially in the context of understanding the Quran\"\n    },\n    {\n      id: \"2\",\n      question: \"Which verse is known as Ayat al-Kursi?\",\n      answer: \"Al-Baqarah 2:255 - the Throne Verse\"\n    }\n  ])\n  \n  const [currentCardIndex, setCurrentCardIndex] = useState(0)\n  const [isFlipped, setIsFlipped] = useState(false)\n  const [studyMode, setStudyMode] = useState<'sequential' | 'random'>('sequential')\n\n  const currentCard = flashcards[currentCardIndex]\n  const hasCards = flashcards.length > 0\n\n  const nextCard = () => {\n    if (studyMode === 'random') {\n      setCurrentCardIndex(Math.floor(Math.random() * flashcards.length))\n    } else {\n      setCurrentCardIndex((prev) => (prev + 1) % flashcards.length)\n    }\n    setIsFlipped(false)\n  }\n\n  const prevCard = () => {\n    setCurrentCardIndex((prev) => (prev - 1 + flashcards.length) % flashcards.length)\n    setIsFlipped(false)\n  }\n\n  const flipCard = () => {\n    setIsFlipped(!isFlipped)\n  }\n\n  const generateFlashcards = () => {\n    // In real app, this would call AI to generate flashcards\n    const newCards: Flashcard[] = [\n      {\n        id: Date.now().toString(),\n        question: `What is the main theme of \"${currentQuery || selectedVerse}\"?`,\n        answer: \"This would be an AI-generated answer based on the query or verse\"\n      }\n    ]\n    setFlashcards(prev => [...prev, ...newCards])\n  }\n\n  return (\n    <div className=\"h-full flex flex-col space-y-4\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <h3 className=\"font-medium\">Flashcards</h3>\n        <div className=\"flex items-center space-x-2\">\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={() => setStudyMode(studyMode === 'sequential' ? 'random' : 'sequential')}\n          >\n            <Shuffle className=\"h-3 w-3 mr-1\" />\n            {studyMode === 'sequential' ? 'Sequential' : 'Random'}\n          </Button>\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={generateFlashcards}\n          >\n            <Plus className=\"h-3 w-3 mr-1\" />\n            Generate\n          </Button>\n        </div>\n      </div>\n\n      {/* Flashcard Display */}\n      {hasCards ? (\n        <div className=\"flex-1 flex flex-col\">\n          {/* Card Counter */}\n          <div className=\"text-center mb-4\">\n            <span className=\"text-sm text-muted-foreground\">\n              Card {currentCardIndex + 1} of {flashcards.length}\n            </span>\n          </div>\n\n          {/* Flashcard */}\n          <div className=\"flex-1 flex items-center justify-center\">\n            <div \n              className=\"w-full max-w-md h-64 cursor-pointer perspective-1000\"\n              onClick={flipCard}\n            >\n              <div className={`relative w-full h-full transition-transform duration-500 transform-style-preserve-3d ${\n                isFlipped ? 'rotate-y-180' : ''\n              }`}>\n                {/* Front of card (Question) */}\n                <Card className=\"absolute inset-0 backface-hidden p-6 flex items-center justify-center border-2 border-primary/20\">\n                  <div className=\"text-center space-y-4\">\n                    <div className=\"w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center mx-auto\">\n                      <span className=\"text-primary font-semibold\">Q</span>\n                    </div>\n                    <p className=\"text-sm font-medium\">{currentCard.question}</p>\n                    <p className=\"text-xs text-muted-foreground\">Click to reveal answer</p>\n                  </div>\n                </Card>\n\n                {/* Back of card (Answer) */}\n                <Card className=\"absolute inset-0 backface-hidden rotate-y-180 p-6 flex items-center justify-center border-2 border-green-200 bg-green-50\">\n                  <div className=\"text-center space-y-4\">\n                    <div className=\"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mx-auto\">\n                      <span className=\"text-green-600 font-semibold\">A</span>\n                    </div>\n                    <p className=\"text-sm\">{currentCard.answer}</p>\n                    <p className=\"text-xs text-muted-foreground\">Click to see question</p>\n                  </div>\n                </Card>\n              </div>\n            </div>\n          </div>\n\n          {/* Controls */}\n          <div className=\"flex items-center justify-between mt-4\">\n            <Button\n              variant=\"outline\"\n              onClick={prevCard}\n              disabled={flashcards.length <= 1}\n            >\n              Previous\n            </Button>\n\n            <div className=\"flex items-center space-x-2\">\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                onClick={flipCard}\n              >\n                {isFlipped ? <EyeOff className=\"h-4 w-4\" /> : <Eye className=\"h-4 w-4\" />}\n              </Button>\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                onClick={() => {\n                  setCurrentCardIndex(0)\n                  setIsFlipped(false)\n                }}\n              >\n                <RotateCcw className=\"h-4 w-4\" />\n              </Button>\n            </div>\n\n            <Button\n              variant=\"outline\"\n              onClick={nextCard}\n              disabled={flashcards.length <= 1}\n            >\n              Next\n            </Button>\n          </div>\n        </div>\n      ) : (\n        <div className=\"flex-1 flex items-center justify-center\">\n          <div className=\"text-center space-y-4 max-w-sm\">\n            <div className=\"w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto\">\n              <RotateCcw className=\"h-8 w-8 text-muted-foreground\" />\n            </div>\n            <div className=\"space-y-2\">\n              <h4 className=\"font-medium\">No flashcards yet</h4>\n              <p className=\"text-sm text-muted-foreground\">\n                Generate flashcards from your study content to test your knowledge\n              </p>\n            </div>\n            <Button onClick={generateFlashcards}>\n              <Plus className=\"h-4 w-4 mr-2\" />\n              Create Flashcards\n            </Button>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AAkBO,SAAS,cAAc,EAAE,aAAa,EAAE,YAAY,EAAsB;;IAC/E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;QACxD;YACE,IAAI;YACJ,UAAU;YACV,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,QAAQ;QACV;KACD;IAED,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B;IAEpE,MAAM,cAAc,UAAU,CAAC,iBAAiB;IAChD,MAAM,WAAW,WAAW,MAAM,GAAG;IAErC,MAAM,WAAW;QACf,IAAI,cAAc,UAAU;YAC1B,oBAAoB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,WAAW,MAAM;QAClE,OAAO;YACL,oBAAoB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,WAAW,MAAM;QAC9D;QACA,aAAa;IACf;IAEA,MAAM,WAAW;QACf,oBAAoB,CAAC,OAAS,CAAC,OAAO,IAAI,WAAW,MAAM,IAAI,WAAW,MAAM;QAChF,aAAa;IACf;IAEA,MAAM,WAAW;QACf,aAAa,CAAC;IAChB;IAEA,MAAM,qBAAqB;QACzB,yDAAyD;QACzD,MAAM,WAAwB;YAC5B;gBACE,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,UAAU,CAAC,2BAA2B,EAAE,gBAAgB,cAAc,EAAE,CAAC;gBACzE,QAAQ;YACV;SACD;QACD,cAAc,CAAA,OAAQ;mBAAI;mBAAS;aAAS;IAC9C;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAc;;;;;;kCAC5B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,aAAa,cAAc,eAAe,WAAW;;kDAEpE,6LAAC,2MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAClB,cAAc,eAAe,eAAe;;;;;;;0CAE/C,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;;kDAET,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;YAOtC,yBACC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;;gCAAgC;gCACxC,mBAAmB;gCAAE;gCAAK,WAAW,MAAM;;;;;;;;;;;;kCAKrD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,WAAU;4BACV,SAAS;sCAET,cAAA,6LAAC;gCAAI,WAAW,CAAC,qFAAqF,EACpG,YAAY,iBAAiB,IAC7B;;kDAEA,6LAAC,mIAAA,CAAA,OAAI;wCAAC,WAAU;kDACd,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAA6B;;;;;;;;;;;8DAE/C,6LAAC;oDAAE,WAAU;8DAAuB,YAAY,QAAQ;;;;;;8DACxD,6LAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;kDAKjD,6LAAC,mIAAA,CAAA,OAAI;wCAAC,WAAU;kDACd,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAA+B;;;;;;;;;;;8DAEjD,6LAAC;oDAAE,WAAU;8DAAW,YAAY,MAAM;;;;;;8DAC1C,6LAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQvD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS;gCACT,UAAU,WAAW,MAAM,IAAI;0CAChC;;;;;;0CAID,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;kDAER,0BAAY,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;iEAAe,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;;;;;;kDAE/D,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;4CACP,oBAAoB;4CACpB,aAAa;wCACf;kDAEA,cAAA,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAIzB,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS;gCACT,UAAU,WAAW,MAAM,IAAI;0CAChC;;;;;;;;;;;;;;;;;qCAML,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;;;;;;sCAEvB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAc;;;;;;8CAC5B,6LAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;sCAI/C,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAS;;8CACf,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;;AAQ/C;GApLgB;KAAA", "debugId": null}}, {"offset": {"line": 2054, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coding%20Project/Tadabbur%20AI/tadabbur-ai-v2/nextjs-app/src/components/notes/quiz-tab.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { Card } from \"@/components/ui/card\"\nimport { Brain, CheckCircle, XCircle, RotateCcw, Plus } from \"lucide-react\"\n\ninterface QuizTabProps {\n  selectedVerse?: string | null\n  currentQuery?: string\n}\n\ninterface QuizQuestion {\n  id: string\n  question: string\n  options: string[]\n  correctAnswer: number\n  explanation: string\n}\n\nexport function QuizTab({ selectedVerse, currentQuery }: QuizTabProps) {\n  const [quizQuestions, setQuizQuestions] = useState<QuizQuestion[]>([\n    {\n      id: \"1\",\n      question: \"What does 'Bismillah' mean?\",\n      options: [\n        \"In the name of <PERSON>\",\n        \"Praise be to <PERSON>\", \n        \"Allah is great\",\n        \"There is no god but <PERSON>\"\n      ],\n      correctAnswer: 0,\n      explanation: \"<PERSON><PERSON><PERSON><PERSON> means 'In the name of <PERSON>' and is recited before starting any good deed.\"\n    }\n  ])\n  \n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)\n  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null)\n  const [showResult, setShowResult] = useState(false)\n  const [score, setScore] = useState(0)\n  const [answeredQuestions, setAnsweredQuestions] = useState<number[]>([])\n\n  const currentQuestion = quizQuestions[currentQuestionIndex]\n  const hasQuestions = quizQuestions.length > 0\n  const isQuizComplete = answeredQuestions.length === quizQuestions.length\n\n  const handleAnswerSelect = (answerIndex: number) => {\n    if (showResult) return\n    setSelectedAnswer(answerIndex)\n  }\n\n  const handleSubmitAnswer = () => {\n    if (selectedAnswer === null) return\n    \n    setShowResult(true)\n    \n    if (selectedAnswer === currentQuestion.correctAnswer) {\n      setScore(prev => prev + 1)\n    }\n    \n    setAnsweredQuestions(prev => [...prev, currentQuestionIndex])\n  }\n\n  const handleNextQuestion = () => {\n    if (currentQuestionIndex < quizQuestions.length - 1) {\n      setCurrentQuestionIndex(prev => prev + 1)\n      setSelectedAnswer(null)\n      setShowResult(false)\n    }\n  }\n\n  const resetQuiz = () => {\n    setCurrentQuestionIndex(0)\n    setSelectedAnswer(null)\n    setShowResult(false)\n    setScore(0)\n    setAnsweredQuestions([])\n  }\n\n  const generateQuiz = () => {\n    // In real app, this would call AI to generate quiz questions\n    const newQuestion: QuizQuestion = {\n      id: Date.now().toString(),\n      question: `What is the main lesson from \"${currentQuery || selectedVerse}\"?`,\n      options: [\n        \"AI-generated option 1\",\n        \"AI-generated option 2\", \n        \"AI-generated option 3\",\n        \"AI-generated option 4\"\n      ],\n      correctAnswer: 0,\n      explanation: \"This would be an AI-generated explanation\"\n    }\n    setQuizQuestions(prev => [...prev, newQuestion])\n  }\n\n  if (!hasQuestions) {\n    return (\n      <div className=\"h-full flex items-center justify-center\">\n        <div className=\"text-center space-y-4 max-w-sm\">\n          <div className=\"w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto\">\n            <Brain className=\"h-8 w-8 text-muted-foreground\" />\n          </div>\n          <div className=\"space-y-2\">\n            <h4 className=\"font-medium\">No quiz questions yet</h4>\n            <p className=\"text-sm text-muted-foreground\">\n              Generate quiz questions from your study content to test your understanding\n            </p>\n          </div>\n          <Button onClick={generateQuiz}>\n            <Plus className=\"h-4 w-4 mr-2\" />\n            Create Quiz\n          </Button>\n        </div>\n      </div>\n    )\n  }\n\n  if (isQuizComplete) {\n    return (\n      <div className=\"h-full flex items-center justify-center\">\n        <Card className=\"p-6 max-w-md w-full text-center space-y-4\">\n          <div className=\"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto\">\n            <Brain className=\"h-8 w-8 text-primary\" />\n          </div>\n          <div className=\"space-y-2\">\n            <h3 className=\"text-xl font-semibold\">Quiz Complete!</h3>\n            <p className=\"text-3xl font-bold text-primary\">\n              {score}/{quizQuestions.length}\n            </p>\n            <p className=\"text-sm text-muted-foreground\">\n              {score === quizQuestions.length \n                ? \"Perfect score! Excellent understanding!\" \n                : score >= quizQuestions.length * 0.7 \n                ? \"Great job! You have a good understanding.\" \n                : \"Keep studying! Review the material and try again.\"}\n            </p>\n          </div>\n          <div className=\"flex space-x-2\">\n            <Button onClick={resetQuiz} variant=\"outline\" className=\"flex-1\">\n              <RotateCcw className=\"h-4 w-4 mr-2\" />\n              Retry\n            </Button>\n            <Button onClick={generateQuiz} className=\"flex-1\">\n              <Plus className=\"h-4 w-4 mr-2\" />\n              More Questions\n            </Button>\n          </div>\n        </Card>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"h-full flex flex-col space-y-4\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <h3 className=\"font-medium\">Quiz</h3>\n        <div className=\"flex items-center space-x-2\">\n          <span className=\"text-sm text-muted-foreground\">\n            Question {currentQuestionIndex + 1} of {quizQuestions.length}\n          </span>\n          <Button variant=\"outline\" size=\"sm\" onClick={generateQuiz}>\n            <Plus className=\"h-3 w-3 mr-1\" />\n            Add Question\n          </Button>\n        </div>\n      </div>\n\n      {/* Progress */}\n      <div className=\"w-full bg-muted rounded-full h-2\">\n        <div \n          className=\"bg-primary h-2 rounded-full transition-all duration-300\"\n          style={{ width: `${(answeredQuestions.length / quizQuestions.length) * 100}%` }}\n        />\n      </div>\n\n      {/* Question */}\n      <Card className=\"flex-1 p-6\">\n        <div className=\"space-y-6\">\n          <h4 className=\"text-lg font-medium\">{currentQuestion.question}</h4>\n          \n          <div className=\"space-y-3\">\n            {currentQuestion.options.map((option, index) => (\n              <button\n                key={index}\n                onClick={() => handleAnswerSelect(index)}\n                disabled={showResult}\n                className={`w-full p-3 text-left rounded-lg border transition-colors ${\n                  selectedAnswer === index\n                    ? showResult\n                      ? index === currentQuestion.correctAnswer\n                        ? \"border-green-500 bg-green-50 text-green-700\"\n                        : \"border-red-500 bg-red-50 text-red-700\"\n                      : \"border-primary bg-primary/5\"\n                    : showResult && index === currentQuestion.correctAnswer\n                    ? \"border-green-500 bg-green-50 text-green-700\"\n                    : \"border-border hover:border-primary/50\"\n                }`}\n              >\n                <div className=\"flex items-center space-x-3\">\n                  <span className=\"w-6 h-6 rounded-full border flex items-center justify-center text-sm\">\n                    {String.fromCharCode(65 + index)}\n                  </span>\n                  <span>{option}</span>\n                  {showResult && selectedAnswer === index && (\n                    index === currentQuestion.correctAnswer ? (\n                      <CheckCircle className=\"h-5 w-5 text-green-500 ml-auto\" />\n                    ) : (\n                      <XCircle className=\"h-5 w-5 text-red-500 ml-auto\" />\n                    )\n                  )}\n                </div>\n              </button>\n            ))}\n          </div>\n\n          {showResult && (\n            <div className=\"p-4 bg-muted rounded-lg\">\n              <p className=\"text-sm font-medium mb-2\">Explanation:</p>\n              <p className=\"text-sm text-muted-foreground\">{currentQuestion.explanation}</p>\n            </div>\n          )}\n        </div>\n      </Card>\n\n      {/* Controls */}\n      <div className=\"flex justify-between\">\n        <Button variant=\"outline\" onClick={resetQuiz}>\n          <RotateCcw className=\"h-4 w-4 mr-2\" />\n          Reset Quiz\n        </Button>\n        \n        {!showResult ? (\n          <Button \n            onClick={handleSubmitAnswer}\n            disabled={selectedAnswer === null}\n          >\n            Submit Answer\n          </Button>\n        ) : (\n          <Button \n            onClick={handleNextQuestion}\n            disabled={currentQuestionIndex >= quizQuestions.length - 1}\n          >\n            Next Question\n          </Button>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AAoBO,SAAS,QAAQ,EAAE,aAAa,EAAE,YAAY,EAAgB;;IACnE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;QACjE;YACE,IAAI;YACJ,UAAU;YACV,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,eAAe;YACf,aAAa;QACf;KACD;IAED,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAEvE,MAAM,kBAAkB,aAAa,CAAC,qBAAqB;IAC3D,MAAM,eAAe,cAAc,MAAM,GAAG;IAC5C,MAAM,iBAAiB,kBAAkB,MAAM,KAAK,cAAc,MAAM;IAExE,MAAM,qBAAqB,CAAC;QAC1B,IAAI,YAAY;QAChB,kBAAkB;IACpB;IAEA,MAAM,qBAAqB;QACzB,IAAI,mBAAmB,MAAM;QAE7B,cAAc;QAEd,IAAI,mBAAmB,gBAAgB,aAAa,EAAE;YACpD,SAAS,CAAA,OAAQ,OAAO;QAC1B;QAEA,qBAAqB,CAAA,OAAQ;mBAAI;gBAAM;aAAqB;IAC9D;IAEA,MAAM,qBAAqB;QACzB,IAAI,uBAAuB,cAAc,MAAM,GAAG,GAAG;YACnD,wBAAwB,CAAA,OAAQ,OAAO;YACvC,kBAAkB;YAClB,cAAc;QAChB;IACF;IAEA,MAAM,YAAY;QAChB,wBAAwB;QACxB,kBAAkB;QAClB,cAAc;QACd,SAAS;QACT,qBAAqB,EAAE;IACzB;IAEA,MAAM,eAAe;QACnB,6DAA6D;QAC7D,MAAM,cAA4B;YAChC,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,UAAU,CAAC,8BAA8B,EAAE,gBAAgB,cAAc,EAAE,CAAC;YAC5E,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,eAAe;YACf,aAAa;QACf;QACA,iBAAiB,CAAA,OAAQ;mBAAI;gBAAM;aAAY;IACjD;IAEA,IAAI,CAAC,cAAc;QACjB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;;;;;;kCAEnB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAc;;;;;;0CAC5B,6LAAC;gCAAE,WAAU;0CAAgC;;;;;;;;;;;;kCAI/C,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAS;;0CACf,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;;;;;IAM3C;IAEA,IAAI,gBAAgB;QAClB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;;;;;;kCAEnB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwB;;;;;;0CACtC,6LAAC;gCAAE,WAAU;;oCACV;oCAAM;oCAAE,cAAc,MAAM;;;;;;;0CAE/B,6LAAC;gCAAE,WAAU;0CACV,UAAU,cAAc,MAAM,GAC3B,4CACA,SAAS,cAAc,MAAM,GAAG,MAChC,8CACA;;;;;;;;;;;;kCAGR,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAS;gCAAW,SAAQ;gCAAU,WAAU;;kDACtD,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGxC,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAS;gCAAc,WAAU;;kDACvC,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;IAO7C;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAc;;;;;;kCAC5B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;;oCAAgC;oCACpC,uBAAuB;oCAAE;oCAAK,cAAc,MAAM;;;;;;;0CAE9D,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;gCAAK,SAAS;;kDAC3C,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAOvC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,WAAU;oBACV,OAAO;wBAAE,OAAO,GAAG,AAAC,kBAAkB,MAAM,GAAG,cAAc,MAAM,GAAI,IAAI,CAAC,CAAC;oBAAC;;;;;;;;;;;0BAKlF,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAuB,gBAAgB,QAAQ;;;;;;sCAE7D,6LAAC;4BAAI,WAAU;sCACZ,gBAAgB,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACpC,6LAAC;oCAEC,SAAS,IAAM,mBAAmB;oCAClC,UAAU;oCACV,WAAW,CAAC,yDAAyD,EACnE,mBAAmB,QACf,aACE,UAAU,gBAAgB,aAAa,GACrC,gDACA,0CACF,gCACF,cAAc,UAAU,gBAAgB,aAAa,GACrD,gDACA,yCACJ;8CAEF,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DACb,OAAO,YAAY,CAAC,KAAK;;;;;;0DAE5B,6LAAC;0DAAM;;;;;;4CACN,cAAc,mBAAmB,SAAS,CACzC,UAAU,gBAAgB,aAAa,iBACrC,6LAAC,8NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;qEAEvB,6LAAC,+MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;oDAEvB;;;;;;;mCA1BG;;;;;;;;;;wBAgCV,4BACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAA2B;;;;;;8CACxC,6LAAC;oCAAE,WAAU;8CAAiC,gBAAgB,WAAW;;;;;;;;;;;;;;;;;;;;;;;0BAOjF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,SAAS;;0CACjC,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;oBAIvC,CAAC,2BACA,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,UAAU,mBAAmB;kCAC9B;;;;;6CAID,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,UAAU,wBAAwB,cAAc,MAAM,GAAG;kCAC1D;;;;;;;;;;;;;;;;;;AAOX;GAvOgB;KAAA", "debugId": null}}, {"offset": {"line": 2578, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coding%20Project/Tadabbur%20AI/tadabbur-ai-v2/nextjs-app/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2614, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coding%20Project/Tadabbur%20AI/tadabbur-ai-v2/nextjs-app/src/components/notes/games-tab.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { Card } from \"@/components/ui/card\"\nimport { Input } from \"@/components/ui/input\"\nimport { Gamepad2, Target, Shuffle, Trophy, Play } from \"lucide-react\"\n\ninterface GamesTabProps {\n  selectedVerse?: string | null\n  currentQuery?: string\n}\n\nexport function GamesTab({ selectedVerse, currentQuery }: GamesTabProps) {\n  const [selectedGame, setSelectedGame] = useState<string | null>(null)\n  const [gameScore, setGameScore] = useState(0)\n  const [isPlaying, setIsPlaying] = useState(false)\n\n  const games = [\n    {\n      id: \"word-match\",\n      title: \"Word Matching\",\n      description: \"Match Arabic words with their English translations\",\n      icon: Target,\n      difficulty: \"Easy\"\n    },\n    {\n      id: \"verse-complete\",\n      title: \"Complete the Verse\",\n      description: \"Fill in the missing words from Quranic verses\",\n      icon: Shuffle,\n      difficulty: \"Medium\"\n    },\n    {\n      id: \"memory-game\",\n      title: \"Memory Challenge\",\n      description: \"Memorize and recall verse sequences\",\n      icon: Trophy,\n      difficulty: \"Hard\"\n    }\n  ]\n\n  const startGame = (gameId: string) => {\n    setSelectedGame(gameId)\n    setIsPlaying(true)\n    setGameScore(0)\n  }\n\n  const endGame = () => {\n    setIsPlaying(false)\n    setSelectedGame(null)\n  }\n\n  if (selectedGame && isPlaying) {\n    return (\n      <div className=\"h-full flex flex-col space-y-4\">\n        {/* Game Header */}\n        <div className=\"flex items-center justify-between\">\n          <h3 className=\"font-medium\">\n            {games.find(g => g.id === selectedGame)?.title}\n          </h3>\n          <div className=\"flex items-center space-x-4\">\n            <span className=\"text-sm text-muted-foreground\">Score: {gameScore}</span>\n            <Button variant=\"outline\" size=\"sm\" onClick={endGame}>\n              End Game\n            </Button>\n          </div>\n        </div>\n\n        {/* Game Content */}\n        <Card className=\"flex-1 p-6\">\n          {selectedGame === \"word-match\" && <WordMatchGame onScoreUpdate={setGameScore} />}\n          {selectedGame === \"verse-complete\" && <VerseCompleteGame onScoreUpdate={setGameScore} />}\n          {selectedGame === \"memory-game\" && <MemoryGame onScoreUpdate={setGameScore} />}\n        </Card>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"h-full flex flex-col space-y-4\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <h3 className=\"font-medium\">Learning Games</h3>\n        <span className=\"text-sm text-muted-foreground\">Choose a game to start</span>\n      </div>\n\n      {/* Games Grid */}\n      <div className=\"flex-1 space-y-4\">\n        {games.map((game) => {\n          const IconComponent = game.icon\n          return (\n            <Card key={game.id} className=\"p-4 hover:shadow-md transition-shadow cursor-pointer\">\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center\">\n                  <IconComponent className=\"h-6 w-6 text-primary\" />\n                </div>\n                <div className=\"flex-1\">\n                  <div className=\"flex items-center space-x-2 mb-1\">\n                    <h4 className=\"font-medium\">{game.title}</h4>\n                    <span className={`text-xs px-2 py-1 rounded-full ${\n                      game.difficulty === \"Easy\" ? \"bg-green-100 text-green-700\" :\n                      game.difficulty === \"Medium\" ? \"bg-yellow-100 text-yellow-700\" :\n                      \"bg-red-100 text-red-700\"\n                    }`}>\n                      {game.difficulty}\n                    </span>\n                  </div>\n                  <p className=\"text-sm text-muted-foreground\">{game.description}</p>\n                </div>\n                <Button onClick={() => startGame(game.id)}>\n                  <Play className=\"h-4 w-4 mr-2\" />\n                  Play\n                </Button>\n              </div>\n            </Card>\n          )\n        })}\n      </div>\n\n      {/* Tips */}\n      <div className=\"p-3 bg-muted rounded-lg\">\n        <p className=\"text-xs text-muted-foreground\">\n          🎮 <strong>Tip:</strong> Games are generated based on your study content. \n          The more you study, the more personalized the games become!\n        </p>\n      </div>\n    </div>\n  )\n}\n\n// Simple Word Match Game Component\nfunction WordMatchGame({ onScoreUpdate }: { onScoreUpdate: (score: number) => void }) {\n  const [matches, setMatches] = useState(0)\n  const [currentPair, setCurrentPair] = useState({ arabic: \"سلام\", english: \"Peace\" })\n\n  const handleMatch = () => {\n    const newScore = matches + 1\n    setMatches(newScore)\n    onScoreUpdate(newScore * 10)\n    // Generate new pair (in real app, this would be dynamic)\n    setCurrentPair({ arabic: \"رحمة\", english: \"Mercy\" })\n  }\n\n  return (\n    <div className=\"text-center space-y-6\">\n      <h4 className=\"text-lg font-medium\">Match the words</h4>\n      <div className=\"space-y-4\">\n        <div className=\"text-2xl arabic-text\">{currentPair.arabic}</div>\n        <div className=\"space-y-2\">\n          <Button variant=\"outline\" onClick={handleMatch} className=\"w-full\">\n            {currentPair.english}\n          </Button>\n          <Button variant=\"outline\" className=\"w-full\">\n            Love\n          </Button>\n          <Button variant=\"outline\" className=\"w-full\">\n            Hope\n          </Button>\n        </div>\n      </div>\n      <p className=\"text-sm text-muted-foreground\">Matches: {matches}</p>\n    </div>\n  )\n}\n\n// Simple Verse Complete Game Component\nfunction VerseCompleteGame({ onScoreUpdate }: { onScoreUpdate: (score: number) => void }) {\n  const [answer, setAnswer] = useState(\"\")\n  const [correct, setCorrect] = useState(0)\n\n  const checkAnswer = () => {\n    if (answer.toLowerCase().includes(\"allah\")) {\n      const newScore = correct + 1\n      setCorrect(newScore)\n      onScoreUpdate(newScore * 15)\n      setAnswer(\"\")\n    }\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <h4 className=\"text-lg font-medium text-center\">Complete the verse</h4>\n      <div className=\"text-center space-y-4\">\n        <p className=\"arabic-text text-xl\">بِسْمِ _____ الرَّحْمَٰنِ الرَّحِيمِ</p>\n        <Input\n          value={answer}\n          onChange={(e) => setAnswer(e.target.value)}\n          placeholder=\"Fill in the missing word\"\n          className=\"text-center\"\n        />\n        <Button onClick={checkAnswer}>Submit</Button>\n      </div>\n      <p className=\"text-sm text-muted-foreground text-center\">Correct answers: {correct}</p>\n    </div>\n  )\n}\n\n// Simple Memory Game Component\nfunction MemoryGame({ onScoreUpdate }: { onScoreUpdate: (score: number) => void }) {\n  const [level, setLevel] = useState(1)\n  const [sequence, setSequence] = useState([\"الله\", \"الرحمن\"])\n\n  return (\n    <div className=\"text-center space-y-6\">\n      <h4 className=\"text-lg font-medium\">Memorize the sequence</h4>\n      <div className=\"space-y-4\">\n        <p className=\"text-sm text-muted-foreground\">Level {level}</p>\n        <div className=\"arabic-text text-xl space-x-2\">\n          {sequence.map((word, index) => (\n            <span key={index} className=\"inline-block mx-2\">{word}</span>\n          ))}\n        </div>\n        <Button onClick={() => {\n          setLevel(level + 1)\n          onScoreUpdate(level * 20)\n        }}>\n          Next Level\n        </Button>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AANA;;;;;;AAaO,SAAS,SAAS,EAAE,aAAa,EAAE,YAAY,EAAiB;;IACrE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,QAAQ;QACZ;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,yMAAA,CAAA,SAAM;YACZ,YAAY;QACd;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,2MAAA,CAAA,UAAO;YACb,YAAY;QACd;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,yMAAA,CAAA,SAAM;YACZ,YAAY;QACd;KACD;IAED,MAAM,YAAY,CAAC;QACjB,gBAAgB;QAChB,aAAa;QACb,aAAa;IACf;IAEA,MAAM,UAAU;QACd,aAAa;QACb,gBAAgB;IAClB;IAEA,IAAI,gBAAgB,WAAW;QAC7B,qBACE,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCACX,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,eAAe;;;;;;sCAE3C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;;wCAAgC;wCAAQ;;;;;;;8CACxD,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,SAAS;8CAAS;;;;;;;;;;;;;;;;;;8BAO1D,6LAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;;wBACb,iBAAiB,8BAAgB,6LAAC;4BAAc,eAAe;;;;;;wBAC/D,iBAAiB,kCAAoB,6LAAC;4BAAkB,eAAe;;;;;;wBACvE,iBAAiB,+BAAiB,6LAAC;4BAAW,eAAe;;;;;;;;;;;;;;;;;;IAItE;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAc;;;;;;kCAC5B,6LAAC;wBAAK,WAAU;kCAAgC;;;;;;;;;;;;0BAIlD,6LAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC;oBACV,MAAM,gBAAgB,KAAK,IAAI;oBAC/B,qBACE,6LAAC,mIAAA,CAAA,OAAI;wBAAe,WAAU;kCAC5B,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAc,WAAU;;;;;;;;;;;8CAE3B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAe,KAAK,KAAK;;;;;;8DACvC,6LAAC;oDAAK,WAAW,CAAC,+BAA+B,EAC/C,KAAK,UAAU,KAAK,SAAS,gCAC7B,KAAK,UAAU,KAAK,WAAW,kCAC/B,2BACA;8DACC,KAAK,UAAU;;;;;;;;;;;;sDAGpB,6LAAC;4CAAE,WAAU;sDAAiC,KAAK,WAAW;;;;;;;;;;;;8CAEhE,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAS,IAAM,UAAU,KAAK,EAAE;;sDACtC,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;uBAnB5B,KAAK,EAAE;;;;;gBAyBtB;;;;;;0BAIF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;;wBAAgC;sCACxC,6LAAC;sCAAO;;;;;;wBAAa;;;;;;;;;;;;;;;;;;AAMlC;GApHgB;KAAA;AAsHhB,mCAAmC;AACnC,SAAS,cAAc,EAAE,aAAa,EAA8C;;IAClF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,QAAQ;QAAQ,SAAS;IAAQ;IAElF,MAAM,cAAc;QAClB,MAAM,WAAW,UAAU;QAC3B,WAAW;QACX,cAAc,WAAW;QACzB,yDAAyD;QACzD,eAAe;YAAE,QAAQ;YAAQ,SAAS;QAAQ;IACpD;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAAsB;;;;;;0BACpC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAAwB,YAAY,MAAM;;;;;;kCACzD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS;gCAAa,WAAU;0CACvD,YAAY,OAAO;;;;;;0CAEtB,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,WAAU;0CAAS;;;;;;0CAG7C,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,WAAU;0CAAS;;;;;;;;;;;;;;;;;;0BAKjD,6LAAC;gBAAE,WAAU;;oBAAgC;oBAAU;;;;;;;;;;;;;AAG7D;IAhCS;MAAA;AAkCT,uCAAuC;AACvC,SAAS,kBAAkB,EAAE,aAAa,EAA8C;;IACtF,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,cAAc;QAClB,IAAI,OAAO,WAAW,GAAG,QAAQ,CAAC,UAAU;YAC1C,MAAM,WAAW,UAAU;YAC3B,WAAW;YACX,cAAc,WAAW;YACzB,UAAU;QACZ;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAAkC;;;;;;0BAChD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAAsB;;;;;;kCACnC,6LAAC,oIAAA,CAAA,QAAK;wBACJ,OAAO;wBACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;wBACzC,aAAY;wBACZ,WAAU;;;;;;kCAEZ,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAS;kCAAa;;;;;;;;;;;;0BAEhC,6LAAC;gBAAE,WAAU;;oBAA4C;oBAAkB;;;;;;;;;;;;;AAGjF;IA7BS;MAAA;AA+BT,+BAA+B;AAC/B,SAAS,WAAW,EAAE,aAAa,EAA8C;;IAC/E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAC;QAAQ;KAAS;IAE3D,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAAsB;;;;;;0BACpC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;;4BAAgC;4BAAO;;;;;;;kCACpD,6LAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,6LAAC;gCAAiB,WAAU;0CAAqB;+BAAtC;;;;;;;;;;kCAGf,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAS;4BACf,SAAS,QAAQ;4BACjB,cAAc,QAAQ;wBACxB;kCAAG;;;;;;;;;;;;;;;;;;AAMX;IAvBS;MAAA", "debugId": null}}, {"offset": {"line": 3190, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coding%20Project/Tadabbur%20AI/tadabbur-ai-v2/nextjs-app/src/components/notes/notes-panel.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from \"react\"\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON> } from \"@/components/ui/tabs\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Textarea } from \"@/components/ui/textarea\"\nimport { SummaryTab } from \"@/components/notes/summary-tab\"\nimport { FlashcardsTab } from \"@/components/notes/flashcards-tab\"\nimport { QuizTab } from \"@/components/notes/quiz-tab\"\nimport { GamesTab } from \"@/components/notes/games-tab\"\nimport { FileText, Zap, Brain, Gamepad2, Save } from \"lucide-react\"\n\ninterface NotesPanelProps {\n  selectedVerse?: string | null\n  currentQuery?: string\n}\n\nexport function NotesPanel({ selectedVerse, currentQuery }: NotesPanelProps) {\n  const [activeTab, setActiveTab] = useState(\"summary\")\n  const [noteContent, setNoteContent] = useState(\"\")\n  const [isGenerating, setIsGenerating] = useState(false)\n\n  // Auto-generate notes when user types or selects a verse\n  useEffect(() => {\n    if (currentQuery && currentQuery.trim().length > 3) {\n      generateNotes()\n    }\n  }, [currentQuery])\n\n  const generateNotes = async () => {\n    setIsGenerating(true)\n    // Simulate AI note generation\n    setTimeout(() => {\n      setNoteContent(`# Study Notes for: \"${currentQuery || selectedVerse}\"\\n\\n## Key Points\\n- This is an AI-generated summary\\n- Based on your query or selected verse\\n- Includes relevant context and insights\\n\\n## Reflection Questions\\n1. What is the main message?\\n2. How does this apply to daily life?\\n3. What other verses relate to this topic?`)\n      setIsGenerating(false)\n    }, 2000)\n  }\n\n  const handleSaveNotes = () => {\n    // Save notes functionality would be implemented here\n    console.log(\"Saving notes:\", noteContent)\n  }\n\n  return (\n    <div className=\"h-full flex flex-col\">\n      {/* Notes Tabs */}\n      <Tabs value={activeTab} onValueChange={setActiveTab} className=\"flex-1 flex flex-col\">\n        <TabsList className=\"grid w-full grid-cols-3 m-4 mb-0\">\n          <TabsTrigger value=\"summary\" className=\"flex items-center space-x-1\">\n            <FileText className=\"h-3 w-3\" />\n            <span className=\"hidden sm:inline\">Summary</span>\n          </TabsTrigger>\n          <TabsTrigger value=\"flashcards\" className=\"flex items-center space-x-1\">\n            <Zap className=\"h-3 w-3\" />\n            <span className=\"hidden sm:inline\">Cards</span>\n          </TabsTrigger>\n          <TabsTrigger value=\"quiz\" className=\"flex items-center space-x-1\">\n            <Brain className=\"h-3 w-3\" />\n            <span className=\"hidden sm:inline\">Quiz</span>\n          </TabsTrigger>\n          {/* <TabsTrigger value=\"games\" className=\"flex items-center space-x-1\">\n            <Gamepad2 className=\"h-3 w-3\" />\n            <span className=\"hidden sm:inline\">Games</span>\n          </TabsTrigger> */}\n        </TabsList>\n\n        {/* Tab Contents */}\n        <div className=\"flex-1 overflow-hidden\">\n          <TabsContent value=\"summary\" className=\"h-full m-0 p-4\">\n            <SummaryTab\n              content={noteContent}\n              onContentChange={setNoteContent}\n              isGenerating={isGenerating}\n              onGenerate={generateNotes}\n              selectedVerse={selectedVerse}\n              currentQuery={currentQuery}\n            />\n          </TabsContent>\n\n          <TabsContent value=\"flashcards\" className=\"h-full m-0 p-4\">\n            <FlashcardsTab\n              selectedVerse={selectedVerse}\n              currentQuery={currentQuery}\n            />\n          </TabsContent>\n\n          <TabsContent value=\"quiz\" className=\"h-full m-0 p-4\">\n            <QuizTab\n              selectedVerse={selectedVerse}\n              currentQuery={currentQuery}\n            />\n          </TabsContent>\n\n          <TabsContent value=\"games\" className=\"h-full m-0 p-4\">\n            <GamesTab\n              selectedVerse={selectedVerse}\n              currentQuery={currentQuery}\n            />\n          </TabsContent>\n        </div>\n      </Tabs>\n\n      {/* Save Button */}\n      {noteContent && (\n        <div className=\"p-4 border-t border-border\">\n          <Button onClick={handleSaveNotes} className=\"w-full\">\n            <Save className=\"h-4 w-4 mr-2\" />\n            Save Notes\n          </Button>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AAVA;;;;;;;;;AAiBO,SAAS,WAAW,EAAE,aAAa,EAAE,YAAY,EAAmB;;IACzE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,yDAAyD;IACzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,gBAAgB,aAAa,IAAI,GAAG,MAAM,GAAG,GAAG;gBAClD;YACF;QACF;+BAAG;QAAC;KAAa;IAEjB,MAAM,gBAAgB;QACpB,gBAAgB;QAChB,8BAA8B;QAC9B,WAAW;YACT,eAAe,CAAC,oBAAoB,EAAE,gBAAgB,cAAc,oRAAoR,CAAC;YACzV,gBAAgB;QAClB,GAAG;IACL;IAEA,MAAM,kBAAkB;QACtB,qDAAqD;QACrD,QAAQ,GAAG,CAAC,iBAAiB;IAC/B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,mIAAA,CAAA,OAAI;gBAAC,OAAO;gBAAW,eAAe;gBAAc,WAAU;;kCAC7D,6LAAC,mIAAA,CAAA,WAAQ;wBAAC,WAAU;;0CAClB,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAU,WAAU;;kDACrC,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAK,WAAU;kDAAmB;;;;;;;;;;;;0CAErC,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAa,WAAU;;kDACxC,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;kDACf,6LAAC;wCAAK,WAAU;kDAAmB;;;;;;;;;;;;0CAErC,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAO,WAAU;;kDAClC,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;wCAAK,WAAU;kDAAmB;;;;;;;;;;;;;;;;;;kCASvC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAU,WAAU;0CACrC,cAAA,6LAAC,gJAAA,CAAA,aAAU;oCACT,SAAS;oCACT,iBAAiB;oCACjB,cAAc;oCACd,YAAY;oCACZ,eAAe;oCACf,cAAc;;;;;;;;;;;0CAIlB,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAa,WAAU;0CACxC,cAAA,6LAAC,mJAAA,CAAA,gBAAa;oCACZ,eAAe;oCACf,cAAc;;;;;;;;;;;0CAIlB,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAO,WAAU;0CAClC,cAAA,6LAAC,6IAAA,CAAA,UAAO;oCACN,eAAe;oCACf,cAAc;;;;;;;;;;;0CAIlB,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAQ,WAAU;0CACnC,cAAA,6LAAC,8IAAA,CAAA,WAAQ;oCACP,eAAe;oCACf,cAAc;;;;;;;;;;;;;;;;;;;;;;;YAOrB,6BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBAAC,SAAS;oBAAiB,WAAU;;sCAC1C,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;;;;;;;;;;AAO7C;GAhGgB;KAAA", "debugId": null}}, {"offset": {"line": 3463, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coding%20Project/Tadabbur%20AI/tadabbur-ai-v2/nextjs-app/src/components/study/study-page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from \"react\"\nimport { Sidebar } from \"@/components/sidebar\"\nimport { ChatInterface } from \"@/components/chat/chat-interface\"\nimport { NotesPanel } from \"@/components/notes/notes-panel\"\nimport { Button } from \"@/components/ui/button\"\nimport { PanelRightOpen, PanelRightClose } from \"lucide-react\"\n\nexport function StudyPage() {\n  const [isNotesPanelOpen, setIsNotesPanelOpen] = useState(true)\n  const [currentQuery, setCurrentQuery] = useState(\"\")\n  const [selectedVerse, setSelectedVerse] = useState<string | null>(null)\n\n  // Mock user data - in real app this would come from auth\n  const currentUser = {\n    name: \"<PERSON>\",\n    email: \"<EMAIL>\"\n  }\n\n  // Mock folders data - in real app this would come from API\n  const folders = [\n    { id: 1, name: \"All notes\", count: 4, icon: \"📝\" },\n    { id: 2, name: \"<PERSON><PERSON>\", count: 2, icon: \"⭐\" },\n    { id: 3, name: \"Prayer\", count: 1, icon: \"🤲\" },\n  ]\n\n  // Auto-open notes panel when user starts typing\n  const handleQueryChange = (query: string) => {\n    setCurrentQuery(query)\n    if (query.trim() && !isNotesPanelOpen) {\n      setIsNotesPanelOpen(true)\n    }\n  }\n\n  return (\n    <div className=\"flex h-screen bg-background\">\n      {/* Sidebar */}\n      <Sidebar \n        folders={folders}\n        currentUser={currentUser}\n        className=\"hidden md:block\"\n      />\n\n      {/* Main Content Area */}\n      <div className=\"flex-1 flex\">\n        {/* Chat Interface */}\n        <div className={`flex-1 transition-all duration-300 ${\n          isNotesPanelOpen ? \"md:w-1/2\" : \"w-full\"\n        }`}>\n          <ChatInterface \n            onQueryChange={handleQueryChange}\n            onVerseSelect={setSelectedVerse}\n            currentQuery={currentQuery}\n          />\n        </div>\n\n        {/* Notes Panel */}\n        <div className={`transition-all duration-300 border-l border-border ${\n          isNotesPanelOpen \n            ? \"w-full md:w-1/2 flex\" \n            : \"w-0 hidden\"\n        }`}>\n          <div className=\"flex-1 flex flex-col\">\n            {/* Notes Panel Header */}\n            <div className=\"flex items-center justify-between p-4 border-b border-border\">\n              <h2 className=\"font-semibold\">Study Notes</h2>\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                onClick={() => setIsNotesPanelOpen(false)}\n              >\n                <PanelRightClose className=\"h-4 w-4\" />\n              </Button>\n            </div>\n\n            {/* Notes Panel Content */}\n            <div className=\"flex-1\">\n              <NotesPanel \n                selectedVerse={selectedVerse}\n                currentQuery={currentQuery}\n              />\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Toggle Notes Panel Button (when closed) */}\n      {!isNotesPanelOpen && (\n        <Button\n          variant=\"outline\"\n          size=\"icon\"\n          className=\"fixed right-4 top-1/2 transform -translate-y-1/2 z-10\"\n          onClick={() => setIsNotesPanelOpen(true)}\n        >\n          <PanelRightOpen className=\"h-4 w-4\" />\n        </Button>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;;;AAPA;;;;;;;AASO,SAAS;;IACd,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElE,yDAAyD;IACzD,MAAM,cAAc;QAClB,MAAM;QACN,OAAO;IACT;IAEA,2DAA2D;IAC3D,MAAM,UAAU;QACd;YAAE,IAAI;YAAG,MAAM;YAAa,OAAO;YAAG,MAAM;QAAK;QACjD;YAAE,IAAI;YAAG,MAAM;YAAa,OAAO;YAAG,MAAM;QAAI;QAChD;YAAE,IAAI;YAAG,MAAM;YAAU,OAAO;YAAG,MAAM;QAAK;KAC/C;IAED,gDAAgD;IAChD,MAAM,oBAAoB,CAAC;QACzB,gBAAgB;QAChB,IAAI,MAAM,IAAI,MAAM,CAAC,kBAAkB;YACrC,oBAAoB;QACtB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,gIAAA,CAAA,UAAO;gBACN,SAAS;gBACT,aAAa;gBACb,WAAU;;;;;;0BAIZ,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAW,CAAC,mCAAmC,EAClD,mBAAmB,aAAa,UAChC;kCACA,cAAA,6LAAC,kJAAA,CAAA,gBAAa;4BACZ,eAAe;4BACf,eAAe;4BACf,cAAc;;;;;;;;;;;kCAKlB,6LAAC;wBAAI,WAAW,CAAC,mDAAmD,EAClE,mBACI,yBACA,cACJ;kCACA,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAgB;;;;;;sDAC9B,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,oBAAoB;sDAEnC,cAAA,6LAAC,mOAAA,CAAA,kBAAe;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAK/B,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,gJAAA,CAAA,aAAU;wCACT,eAAe;wCACf,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQvB,CAAC,kCACA,6LAAC,qIAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,MAAK;gBACL,WAAU;gBACV,SAAS,IAAM,oBAAoB;0BAEnC,cAAA,6LAAC,iOAAA,CAAA,iBAAc;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAKpC;GA3FgB;KAAA", "debugId": null}}]}