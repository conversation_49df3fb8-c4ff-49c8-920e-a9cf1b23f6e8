{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/lib/db/schema.ts"], "sourcesContent": ["import { pgTable, uuid, text, timestamp, boolean, integer, jsonb, serial } from \"drizzle-orm/pg-core\"\nimport { relations, sql } from \"drizzle-orm\"\n\n// Users table (updated for Better Auth)\nexport const user = pgTable(\"user\", {\n  id: text(\"id\").primaryKey(),\n  name: text(\"name\").notNull(),\n  email: text(\"email\").notNull().unique(),\n  emailVerified: boolean(\"email_verified\")\n    .$defaultFn(() => false)\n    .notNull(),\n  image: text(\"image\"),\n  createdAt: timestamp(\"created_at\")\n    .$defaultFn(() => /* @__PURE__ */ new Date())\n    .notNull(),\n  updatedAt: timestamp(\"updated_at\")\n    .$defaultFn(() => /* @__PURE__ */ new Date())\n    .notNull(),\n});\n\n// Sessions table (required by Better Auth)\nexport const session = pgTable(\"session\", {\n  id: text(\"id\").primaryKey(),\n  expiresAt: timestamp(\"expires_at\").notNull(),\n  token: text(\"token\").notNull().unique(),\n  createdAt: timestamp(\"created_at\").notNull(),\n  updatedAt: timestamp(\"updated_at\").notNull(),\n  ipAddress: text(\"ip_address\"),\n  userAgent: text(\"user_agent\"),\n  userId: text(\"user_id\")\n    .notNull()\n    .references(() => user.id, { onDelete: \"cascade\" }),\n});\n\n// Accounts table (required by Better Auth for OAuth)\nexport const account = pgTable(\"account\", {\n  id: text(\"id\").primaryKey(),\n  accountId: text(\"account_id\").notNull(),\n  providerId: text(\"provider_id\").notNull(),\n  userId: text(\"user_id\")\n    .notNull()\n    .references(() => user.id, { onDelete: \"cascade\" }),\n  accessToken: text(\"access_token\"),\n  refreshToken: text(\"refresh_token\"),\n  idToken: text(\"id_token\"),\n  accessTokenExpiresAt: timestamp(\"access_token_expires_at\"),\n  refreshTokenExpiresAt: timestamp(\"refresh_token_expires_at\"),\n  scope: text(\"scope\"),\n  password: text(\"password\"),\n  createdAt: timestamp(\"created_at\").notNull(),\n  updatedAt: timestamp(\"updated_at\").notNull(),\n});\n\n// Verification tokens table (required by Better Auth)\nexport const verificationTokens = pgTable(\"verification_tokens\", {\n  id: uuid(\"id\").primaryKey().default(sql`gen_random_uuid()`),\n  identifier: text(\"identifier\").notNull(),\n  token: text(\"token\").notNull().unique(),\n  expiresAt: timestamp(\"expires_at\").notNull(),\n  createdAt: timestamp(\"created_at\").defaultNow(),\n})\n\n// Folders table\nexport const folders = pgTable(\"folders\", {\n  id: serial(\"id\").primaryKey(),\n  userId: text(\"user_id\").notNull().references(() => user.id, { onDelete: \"cascade\" }),\n  name: text(\"name\").notNull(),\n  noteCount: integer(\"note_count\").default(0),\n  color: text(\"color\").default(\"#10b981\"),\n  createdAt: timestamp(\"created_at\").defaultNow(),\n  updatedAt: timestamp(\"updated_at\").defaultNow(),\n})\n\n// Verses table (existing)\nexport const verses = pgTable(\"verses\", {\n  id: serial(\"id\").primaryKey(),\n  surahNumber: integer(\"surah_number\").notNull(),\n  ayahNumber: integer(\"ayah_number\").notNull(),\n  verseKeys: text(\"verse_keys\").notNull(),\n  textArabic: text(\"text_arabic\").notNull(),\n  textClean: text(\"text_clean\"),\n})\n\nexport const kitabs = pgTable(\"kitabs\", {\n  id: serial(\"id\").primaryKey(),\n  title: text(\"title\").notNull(),\n  translated_title: text(\"translated_title\"),\n  author: text(\"author\").notNull(),\n  translatedBy: text(\"translated_by\"),\n  language: text(\"language\"),\n  createdAt: timestamp(\"created_at\").defaultNow(),\n  updatedAt: timestamp(\"updated_at\").defaultNow(),\n})\n\n// Tafsirs table (existing)\nexport const tafsirs = pgTable(\"tafsirs\", {\n  id: serial(\"id\").primaryKey(),\n  kitabId: integer(\"kitab_id\").notNull().references(() => kitabs.id, { onDelete: \"cascade\" }),\n  verseKeys: text(\"verse_keys\").notNull(),\n  text: text(\"text\").notNull(),\n  language: text(\"language\"),\n  created_at: timestamp(\"created_at\").defaultNow(),\n  updated_at: timestamp(\"updated_at\").defaultNow(),\n})\n\n// Asbab table (existing)\nexport const asbab = pgTable(\"asbab\", {\n  id: serial(\"id\").primaryKey(),\n  kitabId: integer(\"kitab_id\").notNull().references(() => kitabs.id, { onDelete: \"cascade\" }),\n  verseKeys: text(\"verse_keys\").notNull(),\n  text: text(\"text\").notNull(),\n  language: text(\"language\"),\n  createdAt: timestamp(\"created_at\").defaultNow(),\n  updatedAt: timestamp(\"updated_at\").defaultNow(),\n})\n\n// Uploads table (existing)\nexport const uploads = pgTable(\"uploads\", {\n  id: serial(\"id\").primaryKey(),\n  userId: text(\"user_id\").references(() => user.id),\n  attachmentUrl: text(\"attachment_url\").notNull(),\n  ocrText: text(\"ocr_text\"),\n  detectedVersesKeys: text(\"detected_verses_keys\").array(),\n  createdAt: timestamp(\"created_at\").defaultNow(),\n  updatedAt: timestamp(\"updated_at\").defaultNow(),\n})\n\n// Notes table\nexport const notes = pgTable(\"notes\", {\n  id: serial(\"id\").primaryKey(),\n  verseKeys: text(\"verse_keys\").notNull(),\n  userId: text(\"user_id\").notNull().references(() => user.id, { onDelete: \"cascade\" }),\n  folderId: integer(\"folder_id\").references(() => folders.id, { onDelete: \"set null\" }),\n  uploadId: integer(\"upload_id\").references(() => uploads.id, { onDelete: \"set null\" }),\n  tafsirIds: integer(\"tafsir_ids\").array(),\n  asbabIds: text(\"asbab_ids\").array(),\n  title: text(\"title\").notNull(),\n  shortDescription: text(\"short_description\"),\n  content: text(\"content\"),\n  language: text(\"language\"),\n  createdAt: timestamp(\"created_at\").defaultNow(),\n  updatedAt: timestamp(\"updated_at\").defaultNow(),\n})\n\nexport const flashCards = pgTable(\"flash_cards\", {\n  id: serial(\"id\").primaryKey(),\n  userId: text(\"user_id\").references(() => user.id, { onDelete: \"cascade\" }),\n  noteId: integer(\"note_id\").references(() => notes.id, { onDelete: \"cascade\" }),\n  question: text(\"question\").notNull(),\n  answer: text(\"answer\").notNull(),\n  createdAt: timestamp(\"created_at\").defaultNow(),\n})\n\nexport const quizzes = pgTable(\"quizzes\", {\n  id: serial(\"id\").primaryKey(),\n  userId: text(\"user_id\").references(() => user.id, { onDelete: \"cascade\" }),\n  noteId: integer(\"note_id\").references(() => notes.id, { onDelete: \"cascade\" }),\n  question: text(\"question\").notNull(),\n  quizAnswers: jsonb(\"quiz_answers\").notNull(),\n  createdAt: timestamp(\"created_at\").defaultNow(),\n})\n\nexport const translations = pgTable(\"translations\", {\n  id: serial(\"id\").primaryKey(),\n  verseKeys: text(\"verse_keys\").notNull(),\n  language: text(\"language\").notNull(),\n  name: text(\"name\"),\n  translator: text(\"translator\"),\n  text: text(\"text\").notNull(),\n  createdAt: timestamp(\"created_at\").defaultNow(),\n  updatedAt: timestamp(\"updated_at\").defaultNow(),\n})\n\nexport const verification = pgTable(\"verification\", {\n  id: text(\"id\").primaryKey(),\n  identifier: text(\"identifier\").notNull(),\n  value: text(\"value\").notNull(),\n  expiresAt: timestamp(\"expires_at\").notNull(),\n  createdAt: timestamp(\"created_at\").$defaultFn(\n    () => /* @__PURE__ */ new Date()\n  ),\n  updatedAt: timestamp(\"updated_at\").$defaultFn(\n    () => /* @__PURE__ */ new Date()\n  ),\n});\n\n// Relations\nexport const userRelations = relations(user, ({ many }) => ({\n  folders: many(folders),\n  notes: many(notes),\n  uploads: many(uploads),\n  sessions: many(session),\n  accounts: many(account),\n  flashCards: many(flashCards),\n  quizzes: many(quizzes),\n}))\n\nexport const sessionRelations = relations(session, ({ one }) => ({\n  user: one(user, {\n    fields: [session.userId],\n    references: [user.id],\n  }),\n}))\n\nexport const accountRelations = relations(account, ({ one }) => ({\n  user: one(user, {\n    fields: [account.userId],\n    references: [user.id],\n  }),\n}))\n\nexport const foldersRelations = relations(folders, ({ one, many }) => ({\n  user: one(user, {\n    fields: [folders.userId],\n    references: [user.id],\n  }),\n  notes: many(notes),\n}))\n\nexport const notesRelations = relations(notes, ({ one, many }) => ({\n  user: one(user, {\n    fields: [notes.userId],\n    references: [user.id],\n  }),\n  folder: one(folders, {\n    fields: [notes.folderId],\n    references: [folders.id],\n  }),\n  flashCards: many(flashCards),\n  quizzes: many(quizzes),\n}))\n\nexport const uploadsRelations = relations(uploads, ({ one }) => ({\n  user: one(user, {\n    fields: [uploads.userId],\n    references: [user.id],\n  }),\n  note: one(notes, {\n    fields: [uploads.id],\n    references: [notes.uploadId],\n  }),\n}))\n\nexport const flashCardsRelations = relations(flashCards, ({ one }) => ({\n  user: one(user, {\n    fields: [flashCards.userId],\n    references: [user.id],\n  }),\n  note: one(notes, {\n    fields: [flashCards.noteId],\n    references: [notes.id],\n  }),\n}))\n\nexport const quizzesRelations = relations(quizzes, ({ one }) => ({\n  user: one(user, {\n    fields: [quizzes.userId],\n    references: [user.id],\n  }),\n  note: one(notes, {\n    fields: [quizzes.noteId],\n    references: [notes.id],\n  }),\n}))\n\nexport const tafsirRelations = relations(tafsirs, ({ one }) => ({\n  kitab: one(kitabs, {\n    fields: [tafsirs.kitabId],\n    references: [kitabs.id],\n  }),\n}))\n\nexport const asbabRelations = relations(asbab, ({ one }) => ({\n  kitab: one(kitabs, {\n    fields: [asbab.kitabId],\n    references: [kitabs.id],\n  }),\n}))\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;;;AAGO,MAAM,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;IAClC,IAAI,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU;IACzB,MAAM,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,OAAO;IAC1B,OAAO,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO,GAAG,MAAM;IACrC,eAAe,CAAA,GAAA,0KAAA,CAAA,UAAO,AAAD,EAAE,kBACpB,UAAU,CAAC,IAAM,OACjB,OAAO;IACV,OAAO,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE;IACZ,WAAW,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE,cAClB,UAAU,CAAC,IAAM,aAAa,GAAG,IAAI,QACrC,OAAO;IACV,WAAW,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE,cAClB,UAAU,CAAC,IAAM,aAAa,GAAG,IAAI,QACrC,OAAO;AACZ;AAGO,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD,EAAE,WAAW;IACxC,IAAI,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU;IACzB,WAAW,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE,cAAc,OAAO;IAC1C,OAAO,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO,GAAG,MAAM;IACrC,WAAW,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE,cAAc,OAAO;IAC1C,WAAW,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE,cAAc,OAAO;IAC1C,WAAW,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE;IAChB,WAAW,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE;IAChB,QAAQ,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE,WACV,OAAO,GACP,UAAU,CAAC,IAAM,KAAK,EAAE,EAAE;QAAE,UAAU;IAAU;AACrD;AAGO,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD,EAAE,WAAW;IACxC,IAAI,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU;IACzB,WAAW,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO;IACrC,YAAY,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE,eAAe,OAAO;IACvC,QAAQ,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE,WACV,OAAO,GACP,UAAU,CAAC,IAAM,KAAK,EAAE,EAAE;QAAE,UAAU;IAAU;IACnD,aAAa,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE;IAClB,cAAc,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE;IACnB,SAAS,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE;IACd,sBAAsB,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE;IAChC,uBAAuB,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE;IACjC,OAAO,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE;IACZ,UAAU,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE;IACf,WAAW,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE,cAAc,OAAO;IAC1C,WAAW,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE,cAAc,OAAO;AAC5C;AAGO,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD,EAAE,uBAAuB;IAC/D,IAAI,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,GAAG,OAAO,CAAC,oJAAA,CAAA,MAAG,CAAC,iBAAiB,CAAC;IAC1D,YAAY,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO;IACtC,OAAO,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO,GAAG,MAAM;IACrC,WAAW,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE,cAAc,OAAO;IAC1C,WAAW,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU;AAC/C;AAGO,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD,EAAE,WAAW;IACxC,IAAI,CAAA,GAAA,yKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,QAAQ,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,OAAO,GAAG,UAAU,CAAC,IAAM,KAAK,EAAE,EAAE;QAAE,UAAU;IAAU;IAClF,MAAM,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,OAAO;IAC1B,WAAW,CAAA,GAAA,0KAAA,CAAA,UAAO,AAAD,EAAE,cAAc,OAAO,CAAC;IACzC,OAAO,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO,CAAC;IAC7B,WAAW,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU;IAC7C,WAAW,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU;AAC/C;AAGO,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD,EAAE,UAAU;IACtC,IAAI,CAAA,GAAA,yKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,aAAa,CAAA,GAAA,0KAAA,CAAA,UAAO,AAAD,EAAE,gBAAgB,OAAO;IAC5C,YAAY,CAAA,GAAA,0KAAA,CAAA,UAAO,AAAD,EAAE,eAAe,OAAO;IAC1C,WAAW,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO;IACrC,YAAY,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE,eAAe,OAAO;IACvC,WAAW,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE;AAClB;AAEO,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD,EAAE,UAAU;IACtC,IAAI,CAAA,GAAA,yKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,OAAO,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO;IAC5B,kBAAkB,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE;IACvB,QAAQ,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE,UAAU,OAAO;IAC9B,cAAc,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE;IACnB,UAAU,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE;IACf,WAAW,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU;IAC7C,WAAW,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU;AAC/C;AAGO,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD,EAAE,WAAW;IACxC,IAAI,CAAA,GAAA,yKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,SAAS,CAAA,GAAA,0KAAA,CAAA,UAAO,AAAD,EAAE,YAAY,OAAO,GAAG,UAAU,CAAC,IAAM,OAAO,EAAE,EAAE;QAAE,UAAU;IAAU;IACzF,WAAW,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO;IACrC,MAAM,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,OAAO;IAC1B,UAAU,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE;IACf,YAAY,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU;IAC9C,YAAY,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU;AAChD;AAGO,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD,EAAE,SAAS;IACpC,IAAI,CAAA,GAAA,yKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,SAAS,CAAA,GAAA,0KAAA,CAAA,UAAO,AAAD,EAAE,YAAY,OAAO,GAAG,UAAU,CAAC,IAAM,OAAO,EAAE,EAAE;QAAE,UAAU;IAAU;IACzF,WAAW,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO;IACrC,MAAM,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,OAAO;IAC1B,UAAU,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE;IACf,WAAW,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU;IAC7C,WAAW,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU;AAC/C;AAGO,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD,EAAE,WAAW;IACxC,IAAI,CAAA,GAAA,yKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,QAAQ,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,UAAU,CAAC,IAAM,KAAK,EAAE;IAChD,eAAe,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE,kBAAkB,OAAO;IAC7C,SAAS,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE;IACd,oBAAoB,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE,wBAAwB,KAAK;IACtD,WAAW,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU;IAC7C,WAAW,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU;AAC/C;AAGO,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD,EAAE,SAAS;IACpC,IAAI,CAAA,GAAA,yKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,WAAW,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO;IACrC,QAAQ,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,OAAO,GAAG,UAAU,CAAC,IAAM,KAAK,EAAE,EAAE;QAAE,UAAU;IAAU;IAClF,UAAU,CAAA,GAAA,0KAAA,CAAA,UAAO,AAAD,EAAE,aAAa,UAAU,CAAC,IAAM,QAAQ,EAAE,EAAE;QAAE,UAAU;IAAW;IACnF,UAAU,CAAA,GAAA,0KAAA,CAAA,UAAO,AAAD,EAAE,aAAa,UAAU,CAAC,IAAM,QAAQ,EAAE,EAAE;QAAE,UAAU;IAAW;IACnF,WAAW,CAAA,GAAA,0KAAA,CAAA,UAAO,AAAD,EAAE,cAAc,KAAK;IACtC,UAAU,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE,aAAa,KAAK;IACjC,OAAO,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO;IAC5B,kBAAkB,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE;IACvB,SAAS,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE;IACd,UAAU,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE;IACf,WAAW,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU;IAC7C,WAAW,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU;AAC/C;AAEO,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD,EAAE,eAAe;IAC/C,IAAI,CAAA,GAAA,yKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,QAAQ,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,UAAU,CAAC,IAAM,KAAK,EAAE,EAAE;QAAE,UAAU;IAAU;IACxE,QAAQ,CAAA,GAAA,0KAAA,CAAA,UAAO,AAAD,EAAE,WAAW,UAAU,CAAC,IAAM,MAAM,EAAE,EAAE;QAAE,UAAU;IAAU;IAC5E,UAAU,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE,YAAY,OAAO;IAClC,QAAQ,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE,UAAU,OAAO;IAC9B,WAAW,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU;AAC/C;AAEO,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD,EAAE,WAAW;IACxC,IAAI,CAAA,GAAA,yKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,QAAQ,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,UAAU,CAAC,IAAM,KAAK,EAAE,EAAE;QAAE,UAAU;IAAU;IACxE,QAAQ,CAAA,GAAA,0KAAA,CAAA,UAAO,AAAD,EAAE,WAAW,UAAU,CAAC,IAAM,MAAM,EAAE,EAAE;QAAE,UAAU;IAAU;IAC5E,UAAU,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE,YAAY,OAAO;IAClC,aAAa,CAAA,GAAA,wKAAA,CAAA,QAAK,AAAD,EAAE,gBAAgB,OAAO;IAC1C,WAAW,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU;AAC/C;AAEO,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD,EAAE,gBAAgB;IAClD,IAAI,CAAA,GAAA,yKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,WAAW,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO;IACrC,UAAU,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE,YAAY,OAAO;IAClC,MAAM,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE;IACX,YAAY,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE;IACjB,MAAM,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,OAAO;IAC1B,WAAW,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU;IAC7C,WAAW,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU;AAC/C;AAEO,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD,EAAE,gBAAgB;IAClD,IAAI,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU;IACzB,YAAY,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO;IACtC,OAAO,CAAA,GAAA,uKAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO;IAC5B,WAAW,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE,cAAc,OAAO;IAC1C,WAAW,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,CAC3C,IAAM,aAAa,GAAG,IAAI;IAE5B,WAAW,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,CAC3C,IAAM,aAAa,GAAG,IAAI;AAE9B;AAGO,MAAM,gBAAgB,CAAA,GAAA,mJAAA,CAAA,YAAS,AAAD,EAAE,MAAM,CAAC,EAAE,IAAI,EAAE,GAAK,CAAC;QAC1D,SAAS,KAAK;QACd,OAAO,KAAK;QACZ,SAAS,KAAK;QACd,UAAU,KAAK;QACf,UAAU,KAAK;QACf,YAAY,KAAK;QACjB,SAAS,KAAK;IAChB,CAAC;AAEM,MAAM,mBAAmB,CAAA,GAAA,mJAAA,CAAA,YAAS,AAAD,EAAE,SAAS,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QAC/D,MAAM,IAAI,MAAM;YACd,QAAQ;gBAAC,QAAQ,MAAM;aAAC;YACxB,YAAY;gBAAC,KAAK,EAAE;aAAC;QACvB;IACF,CAAC;AAEM,MAAM,mBAAmB,CAAA,GAAA,mJAAA,CAAA,YAAS,AAAD,EAAE,SAAS,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QAC/D,MAAM,IAAI,MAAM;YACd,QAAQ;gBAAC,QAAQ,MAAM;aAAC;YACxB,YAAY;gBAAC,KAAK,EAAE;aAAC;QACvB;IACF,CAAC;AAEM,MAAM,mBAAmB,CAAA,GAAA,mJAAA,CAAA,YAAS,AAAD,EAAE,SAAS,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAK,CAAC;QACrE,MAAM,IAAI,MAAM;YACd,QAAQ;gBAAC,QAAQ,MAAM;aAAC;YACxB,YAAY;gBAAC,KAAK,EAAE;aAAC;QACvB;QACA,OAAO,KAAK;IACd,CAAC;AAEM,MAAM,iBAAiB,CAAA,GAAA,mJAAA,CAAA,YAAS,AAAD,EAAE,OAAO,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAK,CAAC;QACjE,MAAM,IAAI,MAAM;YACd,QAAQ;gBAAC,MAAM,MAAM;aAAC;YACtB,YAAY;gBAAC,KAAK,EAAE;aAAC;QACvB;QACA,QAAQ,IAAI,SAAS;YACnB,QAAQ;gBAAC,MAAM,QAAQ;aAAC;YACxB,YAAY;gBAAC,QAAQ,EAAE;aAAC;QAC1B;QACA,YAAY,KAAK;QACjB,SAAS,KAAK;IAChB,CAAC;AAEM,MAAM,mBAAmB,CAAA,GAAA,mJAAA,CAAA,YAAS,AAAD,EAAE,SAAS,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QAC/D,MAAM,IAAI,MAAM;YACd,QAAQ;gBAAC,QAAQ,MAAM;aAAC;YACxB,YAAY;gBAAC,KAAK,EAAE;aAAC;QACvB;QACA,MAAM,IAAI,OAAO;YACf,QAAQ;gBAAC,QAAQ,EAAE;aAAC;YACpB,YAAY;gBAAC,MAAM,QAAQ;aAAC;QAC9B;IACF,CAAC;AAEM,MAAM,sBAAsB,CAAA,GAAA,mJAAA,CAAA,YAAS,AAAD,EAAE,YAAY,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QACrE,MAAM,IAAI,MAAM;YACd,QAAQ;gBAAC,WAAW,MAAM;aAAC;YAC3B,YAAY;gBAAC,KAAK,EAAE;aAAC;QACvB;QACA,MAAM,IAAI,OAAO;YACf,QAAQ;gBAAC,WAAW,MAAM;aAAC;YAC3B,YAAY;gBAAC,MAAM,EAAE;aAAC;QACxB;IACF,CAAC;AAEM,MAAM,mBAAmB,CAAA,GAAA,mJAAA,CAAA,YAAS,AAAD,EAAE,SAAS,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QAC/D,MAAM,IAAI,MAAM;YACd,QAAQ;gBAAC,QAAQ,MAAM;aAAC;YACxB,YAAY;gBAAC,KAAK,EAAE;aAAC;QACvB;QACA,MAAM,IAAI,OAAO;YACf,QAAQ;gBAAC,QAAQ,MAAM;aAAC;YACxB,YAAY;gBAAC,MAAM,EAAE;aAAC;QACxB;IACF,CAAC;AAEM,MAAM,kBAAkB,CAAA,GAAA,mJAAA,CAAA,YAAS,AAAD,EAAE,SAAS,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QAC9D,OAAO,IAAI,QAAQ;YACjB,QAAQ;gBAAC,QAAQ,OAAO;aAAC;YACzB,YAAY;gBAAC,OAAO,EAAE;aAAC;QACzB;IACF,CAAC;AAEM,MAAM,iBAAiB,CAAA,GAAA,mJAAA,CAAA,YAAS,AAAD,EAAE,OAAO,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QAC3D,OAAO,IAAI,QAAQ;YACjB,QAAQ;gBAAC,MAAM,OAAO;aAAC;YACvB,YAAY;gBAAC,OAAO,EAAE;aAAC;QACzB;IACF,CAAC"}}, {"offset": {"line": 370, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/lib/db/index.ts"], "sourcesContent": ["import { drizzle } from 'drizzle-orm/postgres-js'\nimport postgres from 'postgres'\nimport * as schema from './schema'\n\nif (!process.env.DATABASE_URL) {\n  throw new Error('DATABASE_URL is not set')\n}\n\nconst connectionString = process.env.DATABASE_URL\n\n// Disable prefetch as it is not supported for \"Transaction\" pool mode\nconst client = postgres(connectionString, { prepare: false })\nexport const db = drizzle(client, { schema })\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,IAAI,CAAC,QAAQ,GAAG,CAAC,YAAY,EAAE;IAC7B,MAAM,IAAI,MAAM;AAClB;AAEA,MAAM,mBAAmB,QAAQ,GAAG,CAAC,YAAY;AAEjD,sEAAsE;AACtE,MAAM,SAAS,CAAA,GAAA,gJAAA,CAAA,UAAQ,AAAD,EAAE,kBAAkB;IAAE,SAAS;AAAM;AACpD,MAAM,KAAK,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;IAAE,QAAA;AAAO"}}, {"offset": {"line": 396, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/lib/auth/config.ts"], "sourcesContent": ["import { betterAuth } from \"better-auth\"\nimport { drizzleAdapter } from \"better-auth/adapters/drizzle\"\nimport { db } from \"@/lib/db\"\nimport * as schema from \"@/lib/db/schema\"\n\nexport const auth = betterAuth({\n  database: drizzleAdapter(db, {\n    provider: \"pg\",\n    schema: {\n      user: schema.user,\n      session: schema.session,\n      account: schema.account,\n      verification: schema.verification,\n    },\n  }),\n  emailAndPassword: {\n    enabled: true,\n    requireEmailVerification: false, // Set to true in production\n  },\n  socialProviders: {\n    google: {\n      clientId: process.env.GOOGLE_CLIENT_ID as string,\n      clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,\n    },\n  },\n  session: {\n    expiresIn: 60 * 60 * 24 * 7, // 7 days\n    updateAge: 60 * 60 * 24, // 1 day\n  },\n  user: {\n    additionalFields: {\n      role: {\n        type: \"string\",\n        defaultValue: \"user\",\n        input: false, // Don't allow users to set this\n      },\n    },\n  },\n  trustedOrigins: [\"http://localhost:3000\"],\n})\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AACA;AACA;;;;;AAEO,MAAM,OAAO,CAAA,GAAA,wKAAA,CAAA,aAAU,AAAD,EAAE;IAC7B,UAAU,CAAA,GAAA,0LAAA,CAAA,iBAAc,AAAD,EAAE,iIAAA,CAAA,KAAE,EAAE;QAC3B,UAAU;QACV,QAAQ;YACN,MAAM,kIAAA,CAAA,OAAW;YACjB,SAAS,kIAAA,CAAA,UAAc;YACvB,SAAS,kIAAA,CAAA,UAAc;YACvB,cAAc,kIAAA,CAAA,eAAmB;QACnC;IACF;IACA,kBAAkB;QAChB,SAAS;QACT,0BAA0B;IAC5B;IACA,iBAAiB;QACf,QAAQ;YACN,UAAU,QAAQ,GAAG,CAAC,gBAAgB;YACtC,cAAc,QAAQ,GAAG,CAAC,oBAAoB;QAChD;IACF;IACA,SAAS;QACP,WAAW,KAAK,KAAK,KAAK;QAC1B,WAAW,KAAK,KAAK;IACvB;IACA,MAAM;QACJ,kBAAkB;YAChB,MAAM;gBACJ,MAAM;gBACN,cAAc;gBACd,OAAO;YACT;QACF;IACF;IACA,gBAAgB;QAAC;KAAwB;AAC3C"}}, {"offset": {"line": 451, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from \"next/server\"\r\nimport { auth } from \"@/lib/auth/config\"\r\n\r\nexport async function middleware(request: NextRequest) {\r\n  const session = await auth.api.getSession({\r\n    headers: request.headers,\r\n  })\r\n\r\n  const isAuthPage = request.nextUrl.pathname.startsWith(\"/auth\")\r\n  \r\n  // Comment out the protection temporarily for testing\r\n  // const isProtectedPage = [\"/study\", \"/notes\", \"/profile\"].some(path => \r\n  //   request.nextUrl.pathname.startsWith(path)\r\n  // )\r\n  \r\n  // For testing, allow access to study page\r\n  const isProtectedPage = [\"/notes\", \"/profile\"].some(path => \r\n    request.nextUrl.pathname.startsWith(path)\r\n  )\r\n\r\n  // If user is not authenticated and trying to access protected pages\r\n  if (!session && isProtectedPage) {\r\n    return NextResponse.redirect(new URL(\"/auth/login\", request.url))\r\n  }\r\n\r\n  // If user is authenticated and trying to access auth pages\r\n  if (session && isAuthPage) {\r\n    return NextResponse.redirect(new URL(\"/study\", request.url))\r\n  }\r\n\r\n  return NextResponse.next()\r\n}\r\n\r\nexport const config = {\r\n  matcher: [\r\n    /*\r\n     * Match all request paths except for the ones starting with:\r\n     * - api (API routes)\r\n     * - _next/static (static files)\r\n     * - _next/image (image optimization files)\r\n     * - favicon.ico (favicon file)\r\n     * - public folder\r\n     */\r\n    \"/((?!api|_next/static|_next/image|favicon.ico|logo.png).*)\",\r\n  ],\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;;;AAEO,eAAe,WAAW,OAAoB;IACnD,MAAM,UAAU,MAAM,oIAAA,CAAA,OAAI,CAAC,GAAG,CAAC,UAAU,CAAC;QACxC,SAAS,QAAQ,OAAO;IAC1B;IAEA,MAAM,aAAa,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC;IAEvD,qDAAqD;IACrD,yEAAyE;IACzE,8CAA8C;IAC9C,IAAI;IAEJ,0CAA0C;IAC1C,MAAM,kBAAkB;QAAC;QAAU;KAAW,CAAC,IAAI,CAAC,CAAA,OAClD,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC;IAGtC,oEAAoE;IACpE,IAAI,CAAC,WAAW,iBAAiB;QAC/B,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,eAAe,QAAQ,GAAG;IACjE;IAEA,2DAA2D;IAC3D,IAAI,WAAW,YAAY;QACzB,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU,QAAQ,GAAG;IAC5D;IAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;;KAOC,GACD;KACD;AACH"}}]}