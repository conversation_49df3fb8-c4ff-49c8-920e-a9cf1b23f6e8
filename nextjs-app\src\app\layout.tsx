import type { <PERSON><PERSON><PERSON> } from "next";
import { Inter } from "next/font/google";
import { <PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/components/theme-provider";
import { Toaster } from "@/components/ui/sonner";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-sans",
});

const amiri = Amiri({
  subsets: ["arabic", "latin"],
  weight: ["400", "700"],
  variable: "--font-arabic",
});

export const metadata: Metadata = {
  title: {
    default: "Tadabbur AI - Deep Quranic Contemplation",
    template: "%s | Tadabbur AI"
  },
  description: "Deep Quranic contemplation powered by AI. Explore verses, generate study notes, and deepen your understanding of the Quran with intelligent insights.",
  keywords: ["Quran", "Islam", "AI", "Study", "Tafsir", "Arabic", "Islamic Studies"],
  authors: [{ name: "Tadabbur AI Team" }],
  creator: "Tadabbur AI",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://tadabbur-ai.com",
    title: "Tadabbur AI - Deep Quranic Contemplation",
    description: "Deep Quranic contemplation powered by AI",
    siteName: "Tadabbur AI",
  },
  twitter: {
    card: "summary_large_image",
    title: "Tadabbur AI - Deep Quranic Contemplation",
    description: "Deep Quranic contemplation powered by AI",
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head />
      <body className={`${inter.variable} ${amiri.variable}`}>
        <ThemeProvider defaultTheme="light">
          {children}
          <Toaster />
        </ThemeProvider>
      </body>
    </html>
  )
}

