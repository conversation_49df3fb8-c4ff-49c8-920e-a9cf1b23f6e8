{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/node_modules_better-call_dist_index_5fc3d526.js", "server/edge/chunks/node_modules_zod_dist_esm_56e6e3e4._.js", "server/edge/chunks/node_modules_better-auth_dist_3fc72b3f._.js", "server/edge/chunks/node_modules_jose_dist_webapi_3c9fb492._.js", "server/edge/chunks/node_modules_kysely_dist_esm_67de29e4._.js", "server/edge/chunks/node_modules_drizzle-orm_84e3d8ae._.js", "server/edge/chunks/node_modules_8834b263._.js", "server/edge/chunks/[root-of-the-server]__7b9e1ef3._.js", "server/edge/chunks/edge-wrapper_576a9097.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|logo.png).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|logo.png).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "tRw68C2+BQc7O49cXftW5rYs1RDnDvLjOa57mMMtcSs=", "__NEXT_PREVIEW_MODE_ID": "322f75d72547ac406bd18e5580e89fe3", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "ed81abd7d397a513d8ccbffc5642fa19aecc2782b86de1e0f1ffa40f3a6e9a8a", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "8dbe2d0a3250af9f4601cead5bedae6c893668367a9e8f5ce23a6d8e5b3da2e7"}}}, "sortedMiddleware": ["/"], "functions": {}}